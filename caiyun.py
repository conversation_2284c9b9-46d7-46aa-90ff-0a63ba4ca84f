'''
author: https://github.com/dookcss/python-Script/blob/main/%E7%A7%BB%E5%8A%A8%E4%BA%91%E7%9B%98.py
name: 移动云盘（签到 基础任务 果园 云朵大作战）
使用说明:
  [抓包 Cookie：任意Authorization]
  [注意事项: 简易方法，开抓包进App，搜refresh，找到authTokenRefresh.do ，请求头中的Authorization，响应体<token> xxx</token> 中xxx值（新版加密抓这个）]
环境变量设置:
    - 名称：[ydypCK]   格式：[Authorization值#手机号#token值]
    - 多账号处理方式：[换行或者@分割]
cron: 0 0 8,16,20 * * *
update:
    2024.09.30 First commit
    2025.07.13 更新API域名为 m.mcloud.139.com，支持新版API taskListV2，优化任务处理逻辑
'''

import os
import random
import re
import time
from os import path

import requests
import json
import hashlib

ua = 'Mozilla/5.0 (Linux; Android 15; 23127PN0CC Build/AQ3A.240627.003; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/137.0.7151.117 Mobile Safari/537.36 MCloudApp/12.1.0 AppLanguage/zh-CN'

err_accounts = ''  # 异常账号
err_message = ''  # 错误信息
user_amount = ''  # 用户云朵·数量
redeem_message = ''  # 兑换信息
GLOBAL_DEBUG = False


# 发送通知
def load_send():
    cur_path = path.abspath(path.dirname(__file__))
    notify_file = cur_path + "/notify.py"

    if path.exists(notify_file):
        try:
            from notify import send  # 导入模块的send为notify_send
            print("加载通知服务成功！")
            return send  # 返回导入的函数
        except ImportError:
            print("加载通知服务失败~")
    else:
        print("加载通知服务失败~")

    return False  # 返回False表示未成功加载通知服务


class YP:
    def __init__(self, cookie):
        self.notebook_id = None
        self.note_token = None
        self.note_auth = None
        self.click_num = 15  # 定义抽奖次数和摇一摇戳一戳次数
        self.draw = 1  # 抽奖次数，首次免费
        self.session = requests.Session()

        self.timestamp = str(int(round(time.time() * 1000)))
        self.cookies = {'sensors_stay_time': self.timestamp}
        self.Authorization = cookie.split("#")[0]
        self.account = cookie.split("#")[1]
        self.auth_token = cookie.split("#")[2]
        self.encrypt_account = self.account[:3] + "*" * 4 + self.account[7:]
        self.fruit_url = 'https://happy.mail.10086.cn/jsp/cn/garden/'

        self.jwtHeaders = {
            'User-Agent': ua,
            'Accept': '*/*',
            'Host': 'm.mcloud.139.com',
            'sec-ch-ua-platform': '"Android"',
            'sec-ch-ua': '"Android WebView";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
            'sec-ch-ua-mobile': '?1',
        }
        self.treeHeaders = {
            'Host': 'happy.mail.10086.cn',
            'Accept': 'application/json, text/plain, */*',
            'User-Agent': ua,
            'Referer': 'https://happy.mail.10086.cn/jsp/cn/garden/wap/index.html?sourceid=1003',
            'Cookie': '',
        }
        self.account_valid = True

        # 上传/分享配置
        self.upload_dir_id = ''  # 根目录默认空字符串
        self.share_filename = 'autosign_share.txt'

    # 捕获异常
    
    def catch_errors(func):
        def wrapper(self, *args, **kwargs):
            try:
                return func(self, *args, **kwargs)
            except Exception as e:
                global err_message
                print("错误:", str(e))
                err_message += f'用户[{self.encrypt_account}]:{e}\n'  # 错误信息
            return None

        return wrapper

    @catch_errors
    def run(self):
        if self.jwt():
            self.signin_status()
            self.click()
            # 任务
            print(f'\n📋 云盘任务')
            self.get_tasklist(url = 'sign_in_3', app_type = 'cloud_app')
            print(f'\n🎁 分享文件(尝试)')
            self.share_file()
            print(f'\n☁️ 云朵大作战')
            self.cloud_game()
            print(f'\n🌳 果园任务')
            self.fruitLogin()
            print(f'\n📰 公众号任务')
            self.wxsign()
            print(f'\n🎰 摇一摇抽奖')
            self.shake()
            self.surplus_num()
            print(f'\n🔥 热门任务')
            self.backup_cloud()
            self.open_send()
            print(f'\n📧 139邮箱任务')
            self.get_tasklist(url = 'newsign_139mail', app_type = 'email_app')
            print(f'\n💰 查询云朵数量')
            self.receive()

            if IS_REDEEM and REDEEM_REWARD_DESCRIPTION:
                print('\n🎁 开始兑换奖励')
                reward_list = self.get_redeemable_reward_list()
                if reward_list:
                    found = False
                    for reward in reward_list:
                        if reward.get("prizeName") == REDEEM_REWARD_DESCRIPTION:
                            oid = reward.get("oid")
                            if oid:
                                self.redeem_reward(oid)
                                found = True
                            break
                    if not found:
                        global redeem_message
                        msg = f"❌未找到你想要兑換的奖品: {REDEEM_REWARD_DESCRIPTION}"
                        print(msg)
                        redeem_message = msg
        else:
            # 此处逻辑已移至主函数，避免重复记录
            pass

    @catch_errors
    def send_request(self, url, headers=None, cookies=None, data=None, params=None, method='GET', debug=None,
                     retries=5):

        debug = debug if debug is not None else GLOBAL_DEBUG

        self.session.headers.update(headers or {})
        if cookies:
            self.session.cookies.update(cookies)
        request_args = {'json': data} if isinstance(data, dict) else {'data': data}

        for attempt in range(retries):
            try:
                response = self.session.request(method, url, params = params, **request_args)
                response.raise_for_status()
                if debug:
                    print(f'\n【{url}】响应数据：\n{response.text}')
                return response
            except requests.exceptions.HTTPError as e:
                if e.response.status_code == 401:
                    print(f"鉴权失效 (401): {url}")
                elif e.response.status_code == 403:
                    print(f"访问被拒绝 (403): {url}")
                else:
                    print(f"HTTP错误 {e.response.status_code}: {e}")
                if attempt >= retries - 1:
                    return None
            except (requests.RequestException, ConnectionError, TimeoutError) as e:
                print(f"请求异常: {e}")
                if attempt >= retries - 1:
                    print("达到最大重试次数。")
                    return None
                time.sleep(1)

    # 随机延迟默认1-1.5s
    def sleep(self, min_delay=1, max_delay=1.5):
        delay = random.uniform(min_delay, max_delay)
        time.sleep(delay)

    # 日志
    def log_info(self, err_msg=None, amount=None):
        global err_message, user_amount
        if err_msg is not None:
            err_message += f'用户[{self.encrypt_account}]:{err_msg}\n'  # 错误信息
        elif amount is not None:
            user_amount += f'用户[{self.encrypt_account}]:{amount}\n'  # 云朵数量

    # 刷新令牌
    def sso(self):
        sso_url = 'https://orches.yun.139.com/orchestration/auth-rebuild/token/v1.0/querySpecToken'
        sso_headers = {
            'Authorization': self.Authorization,
            'User-Agent': ua,
            'Content-Type': 'application/json',
            'Accept': '*/*',
            'Host': 'orches.yun.139.com'
        }
        sso_payload = {"account": self.account, "toSourceId": "001005"}
        
        try:
            response = self.send_request(sso_url, headers = sso_headers, data = sso_payload, method = 'POST')
            if not response:
                print(f'错误: SSO请求失败')
                return None
                
            sso_data = response.json()
            
            if sso_data.get('success'):
                refresh_token = sso_data['data']['token']
                return refresh_token
            else:
                print(f'SSO失败: {sso_data.get("message", "未知错误")}')
                return None
        except Exception as e:
            print(f'SSO请求异常: {e}')
            return None

    # jwt
    def jwt(self):
        # 获取jwttoken
        print(f'\n🔐 开始鉴权...')
        print(f'用户: {self.encrypt_account}')
        
        token = self.sso()
        if token is not None:
            jwt_url = f"https://caiyun.feixin.10086.cn:7071/portal/auth/tyrzLogin.action?ssoToken={token}"
            
            try:
                response = self.send_request(jwt_url, headers = self.jwtHeaders, method = 'POST')
                if not response:
                    print('JWT请求失败')
                    self.account_valid = False
                    return False
                    
                jwt_data = response.json()
                
                if jwt_data.get('code') != 0:
                    print(f'JWT鉴权失败: {jwt_data.get("msg", "未知错误")}')
                    self.account_valid = False
                    return False
                    
                self.jwtHeaders['jwtToken'] = jwt_data['result']['token']
                self.cookies['jwtToken'] = jwt_data['result']['token']
                print('✅ 鉴权成功')
                return True
                
            except Exception as e:
                print(f'JWT请求异常: {e}')
                self.account_valid = False
                return False
        else:
            print('❌ Cookie可能失效了')
            self.account_valid = False
            return False

    # 签到查询
    @catch_errors
    def signin_status(self):
        self.sleep()
        # 使用新的域名
        check_url = 'https://m.mcloud.139.com/market/signin/page/info?client=app'
        check_data = self.send_request(check_url, headers = self.jwtHeaders, cookies = self.cookies).json()
        if check_data['msg'] == 'success':
            today_sign_in = check_data['result'].get('todaySignIn', False)

            if today_sign_in:
                print('✅已签到')
            else:
                print('❌ 未签到')
                # 先获取活动配置
                config_url = 'https://m.mcloud.139.com/market/manager/commonMarketconfig/getActivityConfig?marketName=sign_in_3'
                config_data = self.send_request(config_url, headers = self.jwtHeaders,
                                                cookies = self.cookies).json()
                
                if config_data.get('code') == 0:
                    # 执行签到
                    signin_url = 'https://m.mcloud.139.com/market/manager/commonMarketconfig/getByMarketRuleName?marketName=sign_in_3'
                    signin_data = self.send_request(signin_url, headers = self.jwtHeaders,
                                                    cookies = self.cookies).json()

                    if signin_data['msg'] == 'success':
                        print('✅签到成功')
                    else:
                        print(signin_data['msg'])
                        self.log_info(signin_data['msg'])
                else:
                    print('获取签到配置失败')
        else:
            print(check_data['msg'])
            self.log_info(check_data['msg'])

    # 戳一下
    def click(self):
        # 使用新的域名
        url = "https://m.mcloud.139.com/market/signin/task/click?key=task&id=319"
        successful_click = 0  # 获得次数

        try:
            for _ in range(self.click_num):
                return_data = self.send_request(url, headers = self.jwtHeaders, cookies = self.cookies).json()
                time.sleep(0.2)

                if 'result' in return_data:
                    print(f'✅{return_data["result"]}')
                    successful_click += 1

            if successful_click == 0:
                print(f'❌未获得 x {self.click_num}')
        except Exception as e:
            print(f'错误信息:{e}')

    # 刷新笔记token
    @catch_errors
    def refresh_notetoken(self):
        note_url = 'http://mnote.caiyun.feixin.10086.cn/noteServer/api/authTokenRefresh.do'
        note_payload = {
            "authToken": self.auth_token,
            "userPhone": self.account
        }
        note_headers = {
            'X-Tingyun-Id': 'p35OnrDoP8k;c=2;r=**********;u=43ee994e8c3a6057970124db00b2442c::8B3D3F05462B6E4C',
            'Charset': 'UTF-8',
            'Connection': 'Keep-Alive',
            'User-Agent': 'mobile',
            'APP_CP': 'android',
            'CP_VERSION': '3.2.0',
            'x-huawei-channelsrc': '********',
            'Host': 'mnote.caiyun.feixin.10086.cn',
            'Content-Type': 'application/json; charset=UTF-8',
            'Accept-Encoding': 'gzip'
        }

        try:
            response = self.send_request(note_url, headers = note_headers, data = note_payload, method = "POST")
            response.raise_for_status()
        except requests.exceptions.RequestException as e:
            print('出错了:', e)
            return

        self.note_token = response.headers.get('NOTE_TOKEN')
        self.note_auth = response.headers.get('APP_AUTH')

    # 获取任务列表
    def get_tasklist(self, url, app_type):
        # 使用新版API taskListV2
        base_url = 'https://m.mcloud.139.com/market/signin/task/taskListV2'
        client_version = '12.1.0'
        
        # 获取所有任务组
        task_groups = ['new', 'day', 'month', 'time'] if app_type == 'cloud_app' else ['month']
        all_tasks = {}
        
        for group in task_groups:
            group_url = f'{base_url}?marketname={url}&clientVersion={client_version}&group={group}'
            try:
                return_data = self.send_request(group_url, headers = self.jwtHeaders, cookies = self.cookies).json()
                if return_data and return_data.get('code') == 0:
                    group_tasks = return_data.get('result', {}).get(group, [])
                    if group_tasks:
                        all_tasks[group] = group_tasks
                self.sleep(0.5, 1)
            except Exception as e:
                print(f'获取{group}任务组失败: {e}')
        
        # 任务列表
        task_list = all_tasks

        try:
            for task_type, tasks in task_list.items():
                if app_type == 'cloud_app':
                    if task_type == "new":
                        print('\n🎯 云盘新任务')
                        for task in tasks:
                            task_id = task.get('id')
                            task_name = task.get('name', '')
                            task_status = task.get('state', '')
                            
                            if task_status == 'FINISH':
                                print(f'-已完成: {task_name}')
                                continue
                            print(f'-去完成: {task_name}')
                            self.do_task(task_id, task_type = 'new', app_type = 'cloud_app')
                            time.sleep(2)
                    elif task_type == "month":
                        print('\n📆 云盘每月任务')
                        for month in tasks:
                            task_id = month.get('id')
                            if task_id in [110, 113, 417, 409]:
                                continue
                            task_name = month.get('name', '')
                            task_status = month.get('state', '')

                            if task_status == 'FINISH':
                                print(f'-已完成: {task_name}')
                                continue
                            print(f'-去完成: {task_name}')
                            self.do_task(task_id, task_type = 'month', app_type = 'cloud_app')
                            time.sleep(2)
                    elif task_type == "day":
                        print('\n📆 云盘每日任务')
                        for day in tasks:
                            task_id = day.get('id')
                            if task_id == 404:
                                continue
                            task_name = day.get('name')
                            task_status = day.get('state', '')

                            if task_status == 'FINISH':
                                print(f'-已完成: {task_name}')
                                continue
                            print(f'-去完成: {task_name}')
                            self.do_task(task_id, task_type = 'day', app_type = 'cloud_app')
                    elif task_type == "time":
                        print('\n⏰ 云盘限时任务')
                        for task in tasks:
                            task_id = task.get('id')
                            task_name = task.get('name', '')
                            task_status = task.get('state', '')
                            
                            if task_status == 'FINISH':
                                print(f'-已完成: {task_name}')
                                continue
                            print(f'-去完成: {task_name}')
                            self.do_task(task_id, task_type = 'time', app_type = 'cloud_app')
                            time.sleep(2)
                elif app_type == 'email_app':
                    if task_type == "month":
                        print('\n📆 139邮箱每月任务')
                        for month in tasks:
                            task_id = month.get('id')
                            task_name = month.get('name', '')
                            task_status = month.get('state', '')
                            if task_id in [1004, 1005, 1015, 1020]:
                                continue

                            if task_status == 'FINISH':
                                print(f'-已完成: {task_name}')
                                continue
                            print(f'-去完成: {task_name}')
                            self.do_task(task_id, task_type = 'month', app_type = 'email_app')
                            time.sleep(2)
        except Exception as e:
            print(f'错误信息:{e}')

    # 做任务
    @catch_errors
    def do_task(self, task_id, task_type, app_type):
        self.sleep()
        # 使用新的域名 m.mcloud.139.com
        task_url = f'https://m.mcloud.139.com/market/signin/task/click?key=task&id={task_id}'
        response = self.send_request(task_url, headers = self.jwtHeaders, cookies = self.cookies)
        if response and response.json().get('code') == 0:
            print(f'  ✓ 任务点击成功')
        else:
            print(f'  × 任务点击失败')

        if app_type == 'cloud_app':
            if task_type == 'day':
                if task_id == 106:
                    print('-开始上传文件，默认0kb')
                    self.updata_file()
                elif task_id == 107:
                    self.refresh_notetoken()
                    print('-获取默认笔记id')
                    note_url = 'http://mnote.caiyun.feixin.10086.cn/noteServer/api/syncNotebookV3.do'
                    headers = {
                        'X-Tingyun-Id': 'p35OnrDoP8k;c=2;r=**********;u=43ee994e8c3a6057970124db00b2442c::8B3D3F05462B6E4C',
                        'Charset': 'UTF-8',
                        'Connection': 'Keep-Alive',
                        'User-Agent': 'mobile',
                        'APP_CP': 'android',
                        'CP_VERSION': '3.2.0',
                        'x-huawei-channelsrc': '********',
                        'APP_NUMBER': self.account,
                        'APP_AUTH': self.note_auth,
                        'NOTE_TOKEN': self.note_token,
                        'Host': 'mnote.caiyun.feixin.10086.cn',
                        'Content-Type': 'application/json; charset=UTF-8',
                        'Accept': '*/*'
                    }
                    payload = {
                        "addNotebooks": [],
                        "delNotebooks": [],
                        "notebookRefs": [],
                        "updateNotebooks": []
                    }
                    return_data = self.send_request(url = note_url, headers = headers, data = payload,
                                                    method = 'POST').json()
                    if return_data is None:
                        return print('出错了')
                    self.notebook_id = return_data['notebooks'][0]['notebookId']
                    print('开始创建笔记')
                    self.create_note(headers)
            elif task_type == 'month':
                pass
        elif app_type == 'email_app':
            if task_type == 'month':
                pass

    # 上传文件
    @catch_errors
    def updata_file(self):
        url = 'http://ose.caiyun.feixin.10086.cn/richlifeApp/devapp/IUploadAndDownload'
        headers = {
            'x-huawei-uploadSrc': '1',
            'x-ClientOprType': '11',
            'Connection': 'keep-alive',
            'x-NetType': '6',
            'x-DeviceInfo': '6|127.0.0.1|1|10.0.1|Xiaomi|M2012K10C|********************************|02-00-00-00-00-00|android 11|1080X2272|zh||||032|',
            'x-huawei-channelSrc': '********',
            'x-MM-Source': '032',
            'x-SvcType': '1',
            'APP_NUMBER': self.account,
            'Authorization': self.Authorization,
            'X-Tingyun-Id': 'p35OnrDoP8k;c=2;r=**********;u=43ee994e8c3a6057970124db00b2442c::8B3D3F05462B6E4C',
            'Host': 'ose.caiyun.feixin.10086.cn',
            'User-Agent': 'okhttp/3.11.0',
            'Content-Type': 'application/xml; charset=UTF-8',
            'Accept': '*/*'
        }
        payload = '''
                                <pcUploadFileRequest>
                                    <ownerMSISDN>{phone}</ownerMSISDN>
                                    <fileCount>1</fileCount>
                                    <totalSize>1</totalSize>
                                    <uploadContentList length="1">
                                        <uploadContentInfo>
                                            <comlexFlag>0</comlexFlag>
                                            <contentDesc><![CDATA[]]></contentDesc>
                                            <contentName><![CDATA[000000.txt]]></contentName>
                                            <contentSize>1</contentSize>
                                            <contentTAGList></contentTAGList>
                                            <digest>C4CA4238A0B923820DCC509A6F75849B</digest>
                                            <exif/>
                                            <fileEtag>0</fileEtag>
                                            <fileVersion>0</fileVersion>
                                            <updateContentID></updateContentID>
                                        </uploadContentInfo>
                                    </uploadContentList>
                                    <newCatalogName></newCatalogName>
                                    <parentCatalogID></parentCatalogID>
                                    <operation>0</operation>
                                    <path></path>
                                    <manualRename>2</manualRename>
                                    <autoCreatePath length="0"/>
                                    <tagID></tagID>
                                    <tagType></tagType>
                                </pcUploadFileRequest>
                            '''.format(phone = self.account)

        response = requests.post(url = url, headers = headers, data = payload)
        if response is None:
            return
        if response.status_code != 200:
            return print('-上传失败')
        print('-上传文件成功')

    # 创建笔记
    def create_note(self, headers):
        note_id = self.get_note_id(32)  # 获取随机笔记id
        createtime = str(int(round(time.time() * 1000)))
        time.sleep(3)
        updatetime = str(int(round(time.time() * 1000)))
        note_url = 'http://mnote.caiyun.feixin.10086.cn/noteServer/api/createNote.do'
        payload = {
            "archived": 0,
            "attachmentdir": note_id,
            "attachmentdirid": "",
            "attachments": [],
            "audioInfo": {
                "audioDuration": 0,
                "audioSize": 0,
                "audioStatus": 0
            },
            "contentid": "",
            "contents": [{
                "contentid": 0,
                "data": "<font size=\"3\">000000</font>",
                "noteId": note_id,
                "sortOrder": 0,
                "type": "RICHTEXT"
            }],
            "cp": "",
            "createtime": createtime,
            "description": "android",
            "expands": {
                "noteType": 0
            },
            "latlng": "",
            "location": "",
            "noteid": note_id,
            "notestatus": 0,
            "remindtime": "",
            "remindtype": 1,
            "revision": "1",
            "sharecount": "0",
            "sharestatus": "0",
            "system": "mobile",
            "tags": [{
                "id": self.notebook_id,
                "orderIndex": "0",
                "text": "默认笔记本"
            }],
            "title": "00000",
            "topmost": "0",
            "updatetime": updatetime,
            "userphone": self.account,
            "version": "1.00",
            "visitTime": ""
        }
        create_note_data = self.send_request(note_url, headers = headers, data = payload, method = "POST")
        if create_note_data.status_code == 200:
            print('-创建笔记成功')
        else:
            print('-创建失败')

    # 笔记id
    def get_note_id(self, length):
        characters = '19f3a063d67e4694ca63a4227ec9a94a19088404f9a28084e3e486b928039a299bf756ebc77aa4f6bfa250308ec6a8be8b63b5271a00350d136d117b8a72f39c5bd15cdfd350cba4271dc797f15412d9f269e666aea5039f5049d00739b320bb9e8585a008b52c1cbd86970cae9476446f3e41871de8d9f6112db94b05e5dc7ea0a942a9daf145ac8e487d3d5cba7cea145680efc64794d43dd15c5062b81e1cda7bf278b9bc4e1b8955846e6bc4b6a61c28f831f81b2270289e5a8a677c3141ddc9868129060c0c3b5ef507fbd46c004f6de346332ef7f05c0094215eae1217ee7c13c8dca6d174cfb49c716dd42903bb4b02d823b5f1ff93c3f88768251b56cc'
        note_id = ''.join(random.choice(characters) for _ in range(length))
        return note_id

    # 公众号签到
    @catch_errors
    def wxsign(self):
        self.sleep()
        # 使用新的域名
        url = 'https://m.mcloud.139.com/market/playoffic/followSignInfo?isWx=true'
        return_data = self.send_request(url, headers = self.jwtHeaders, cookies = self.cookies).json()

        if return_data['msg'] != 'success':
            return print(return_data['msg'])
        if not return_data['result'].get('todaySignIn'):
            return print('❌签到失败,可能未绑定公众号')
        return print('✅签到成功')

    # 摇一摇
    def shake(self):
        # 使用新的域名
        url = "https://m.mcloud.139.com/market/shake-server/shake/shakeIt?flag=1"
        successful_shakes = 0  # 记录成功摇中的次数

        try:
            for _ in range(self.click_num):
                return_data = self.send_request(url = url, cookies = self.cookies, headers = self.jwtHeaders,
                                                method = 'POST').json()
                time.sleep(1)
                shake_prize_config = return_data["result"].get("shakePrizeconfig")

                if shake_prize_config:
                    print(f"🎉摇一摇获得: {shake_prize_config['name']}")
                    successful_shakes += 1
        except Exception as e:
            print(f'错误信息: {e}')
        if successful_shakes == 0:
            print(f'❌未摇中 x {self.click_num}')

    # 查询剩余抽奖次数
    @catch_errors
    def surplus_num(self):
        self.sleep()
        # 使用新的域名
        draw_info_url = 'https://m.mcloud.139.com/market/playoffic/drawInfo'
        draw_url = "https://m.mcloud.139.com/market/playoffic/draw"

        draw_info_data = self.send_request(draw_info_url, headers = self.jwtHeaders).json()

        if draw_info_data.get('msg') == 'success':
            remain_num = draw_info_data['result'].get('surplusNumber', 0)
            print(f'剩余抽奖次数{remain_num}')
            if remain_num > 50 - self.draw:
                for _ in range(self.draw):
                    self.sleep()
                    draw_data = self.send_request(url = draw_url, headers = self.jwtHeaders).json()

                    if draw_data.get("code") == 0:
                        prize_name = draw_data["result"].get("prizeName", "")
                        print("✅抽奖成功，获得:" + prize_name)
                    else:
                        print("❌抽奖失败")
            else:
                pass
        else:
            msg = draw_info_data.get('msg')
            print(msg)
            if msg and '长期未登录' in msg:
                self.account_valid = False
            self.log_info(msg)

    # 果园专区
    @catch_errors
    def fruitLogin(self):
        token = self.sso()
        if token is not None:
            print("-果园专区token刷新成功")
            self.sleep()
            login_info_url = f'{self.fruit_url}login/caiyunsso.do?token={token}&account={self.account}&targetSourceId=001208&sourceid=1003&enableShare=1'
            headers = {
                'Host': 'happy.mail.10086.cn',
                'Upgrade-Insecure-Requests': '1',
                'User-Agent': ua,
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
                'Referer': 'https://caiyun.feixin.10086.cn:7071/',
                'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7'
            }
            try:
                loginInfoData = requests.get(login_info_url, headers=headers, allow_redirects=True, timeout=10)
                loginInfoData.raise_for_status()
                cookies_dict = loginInfoData.cookies.get_dict()
                treeCookie = '; '.join([f'{k}={v}' for k, v in cookies_dict.items()])
                self.treeHeaders['Cookie'] = treeCookie
            except requests.exceptions.RequestException as e:
                print(f'❌果园登录请求失败: {e}')
                return

            do_login_url = f'{self.fruit_url}login/userinfo.do'
            doLoginData = self.send_request(do_login_url, headers = self.treeHeaders).json()
            if doLoginData.get('result', {}).get('islogin') != 1:
                return print('❌果园登录失败')
            # 去做果园任务
            self.fruitTask()
        else:
            print("果园专区token刷新失败")

    # 任务查询
    @catch_errors
    def fruitTask(self):
        # 签到任务
        check_sign_data = self.send_request(f'{self.fruit_url}task/checkinInfo.do',
                                            headers = self.treeHeaders).json()
        if check_sign_data.get('success'):
            today_checkin = check_sign_data.get('result', {}).get('todayCheckin', 0)
            if today_checkin == 1:
                print('-果园今日已签到')
            else:
                checkin_data = self.send_request(f'{self.fruit_url}task/checkin.do',
                                                 headers = self.treeHeaders).json()
                if checkin_data.get('result', {}).get('code', '') == 1:
                    print('-果园签到成功')
                self.sleep()
                water_data = self.send_request(f'{self.fruit_url}user/clickCartoon.do?cartoonType=widget',
                                               headers = self.treeHeaders).json()
                color_data = self.send_request(f'{self.fruit_url}user/clickCartoon.do?cartoonType=color',
                                               headers = self.treeHeaders).json()
                given_water = water_data.get('result', {}).get('given', 0)
                print(f'-领取每日水滴: {given_water}')
                print(f'-每日雨滴:{color_data.get("result").get("msg")}')
        else:
            print('-果园签到查询失败:', check_sign_data.get('msg', ''))

        # 获取任务列表
        task_list_data = self.send_request(f'{self.fruit_url}task/taskList.do?clientType=PE',
                                           headers = self.treeHeaders).json()
        task_state_data = self.send_request(f'{self.fruit_url}task/taskState.do', headers = self.treeHeaders).json()
        task_state_result = task_state_data.get('result', [])

        task_list = task_list_data.get('result', [])

        for task in task_list:
            task_id = task.get('taskId', '')
            task_name = task.get('taskName', '')
            water_num = task.get('waterNum', 0)
            if task_id == 2002 or task_id == 2003:
                continue

            task_state = next(
                (state.get('taskState', 0) for state in task_state_result if state.get('taskId') == task_id), 0)

            if task_state == 2:
                print(f'-已完成: {task_name}')
            else:
                self.do_fruit_task(task_name, task_id, water_num)

        # 果树信息
        self.tree_info()

    # 做任务
    @catch_errors
    def do_fruit_task(self, task_name, task_id, water_num):
        print(f'-去完成: {task_name}')
        do_task_url = f'{self.fruit_url}task/doTask.do?taskId={task_id}'
        do_task_data = self.send_request(do_task_url, headers = self.treeHeaders).json()

        if do_task_data.get('success'):
            get_water_url = f'{self.fruit_url}task/givenWater.do?taskId={task_id}'
            get_water_data = self.send_request(get_water_url, headers = self.treeHeaders).json()

            if get_water_data.get('success'):
                print(f'-已完成任务获得水滴: {water_num}')
            else:
                print(f'❌领取失败: {get_water_data.get("msg", "")}')
        else:
            print(f'❌参与任务失败: {do_task_data.get("msg", "")}')

    # 果树信息
    @catch_errors
    def tree_info(self):
        treeinfo_url = f'{self.fruit_url}user/treeInfo.do'
        treeinfo_data = self.send_request(treeinfo_url, headers = self.treeHeaders).json()

        if not treeinfo_data.get('success'):
            error_message = treeinfo_data.get('msg', '获取果园任务列表失败')
            print(error_message)
        else:
            collect_water = treeinfo_data.get('result', {}).get('collectWater', 0)
            tree_level = treeinfo_data.get('result', {}).get('treeLevel', 0)
            print(f'-当前小树等级: {tree_level} 剩余水滴: {collect_water}')
            if tree_level in (2, 4, 6, 8):
                # 开宝箱
                openbox_url = f'{self.fruit_url}prize/openBox.do'
                openbox_data = self.send_request(openbox_url, headers = self.treeHeaders).json()
                print(f'- {openbox_data.get("result").get("msg")}')

            watering_amount = collect_water // 20  # 计算需要浇水的次数
            watering_url = f'{self.fruit_url}user/watering.do?isFast=0'
            if watering_amount > 0:
                for _ in range(watering_amount):
                    watering_data = self.send_request(watering_url, headers = self.treeHeaders).json()
                    if watering_data.get('success'):
                        print('✔️ 浇水成功')
                        time.sleep(3)
            else:
                print('-水滴不足!')

    # 云朵大作战
    @catch_errors
    def cloud_game(self):
        # 使用新的域名
        game_info_url = 'https://m.mcloud.139.com/market/signin/hecheng1T/info?op=info'
        bigin_url = 'https://m.mcloud.139.com/market/signin/hecheng1T/beinvite'
        end_url = 'https://m.mcloud.139.com/market/signin/hecheng1T/finish?flag=true'

        game_info_data = self.send_request(game_info_url, headers = self.jwtHeaders, cookies = self.cookies).json()
        if game_info_data and game_info_data.get('code', -1) == 0:
            currnum = game_info_data.get('result', {}).get('info', {}).get('curr', 0)
            count = game_info_data.get('result', {}).get('history', {}).get('0', {}).get('count', '')
            rank = game_info_data.get('result', {}).get('history', {}).get('0', {}).get('rank', '')

            print(f'今日剩余游戏次数: {currnum}\n本月排名: {rank}    合成次数: {count}')

            for _ in range(currnum):
                self.send_request(bigin_url, headers = self.jwtHeaders, cookies = self.cookies).json()
                print('-开始游戏,等待10-15秒完成游戏')
                time.sleep(random.randint(10, 15))
                end_data = self.send_request(end_url, headers = self.jwtHeaders, cookies = self.cookies).json()
                if end_data and end_data.get('code', -1) == 0:
                    print('游戏成功')
        else:
            print("-获取游戏信息失败")

    # 领取云朵
    @catch_errors
    def receive(self):
        # 使用新的域名
        receive_url = "https://m.mcloud.139.com/market/signin/page/receive"
        prize_url = f"https://m.mcloud.139.com/market/prizeApi/checkPrize/getUserPrizeLogPage?currPage=1&pageSize=15&_{self.timestamp}"
        receive_data = self.send_request(receive_url, headers = self.jwtHeaders, cookies = self.cookies).json()
        self.sleep()
        prize_data = self.send_request(prize_url, headers = self.jwtHeaders, cookies = self.cookies).json()
        result = prize_data.get('result').get('result')
        rewards = ''
        for value in result:
            prizeName = value.get('prizeName')
            flag = value.get('flag')
            if flag == 1:
                rewards += f'-待领取奖品: {prizeName}\n'

        receive_amount = receive_data["result"].get("receive", "")
        total_amount = receive_data["result"].get("total", "")
        print(f'\n-当前待领取:{receive_amount}云朵')
        print(f'-当前云朵数量:{total_amount}云朵')
        msg = f'云朵数量:{total_amount} \n{rewards}'
        self.log_info(amount = msg)

    # 备份云朵
    @catch_errors
    def backup_cloud(self):
        # 使用新的域名
        backup_url = 'https://m.mcloud.139.com/market/backupgift/info'
        backup_data = self.send_request(backup_url, headers = self.jwtHeaders).json()
        state = backup_data.get('result', {}).get('state', '')
        if state == -1:
            print('本月未备份,暂无连续备份奖励')

        elif state == 0:
            print('-领取本月连续备份奖励')
            cur_url = 'https://m.mcloud.139.com/market/backupgift/receive'
            cur_data = self.send_request(cur_url, headers = self.jwtHeaders).json()
            print(f'-获得云朵数量:{cur_data.get("result").get("result")}')

        elif state == 1:
            print('-已领取本月连续备份奖励')
        self.sleep()
        expend_url = 'https://m.mcloud.139.com/market/signin/page/taskExpansion'  # 每月膨胀云朵
        expend_data = self.send_request(expend_url, headers = self.jwtHeaders, cookies = self.cookies).json()

        curMonthBackup = expend_data.get('result', {}).get('curMonthBackup', '')  # 本月备份
        preMonthBackup = expend_data.get('result', {}).get('preMonthBackup', '')  # 上月备份
        curMonthBackupTaskAccept = expend_data.get('result', {}).get('curMonthBackupTaskAccept', '')  # 本月是否领取
        nextMonthTaskRecordCount = expend_data.get('result', {}).get('nextMonthTaskRecordCount', '')  # 下月备份云朵
        acceptDate = expend_data.get('result', {}).get('acceptDate', '')  # 月份

        if curMonthBackup:
            print(f'- 本月已备份，下月可领取膨胀云朵: {nextMonthTaskRecordCount}')
        else:
            print('- 本月还未备份，下月暂无膨胀云朵')

        if preMonthBackup:
            if curMonthBackupTaskAccept:
                print('- 上月已备份，膨胀云朵已领取')
            else:
                # 领取
                receive_url = f'https://m.mcloud.139.com/market/signin/page/receiveTaskExpansion?acceptDate={acceptDate}'
                receive_data = self.send_request(receive_url, headers = self.jwtHeaders,
                                                 cookies = self.cookies).json()
                if receive_data.get("code") != 0:
                    print(f'-领取失败:{receive_data.get("msg")}')
                else:
                    cloudCount = receive_data.get('result', {}).get('cloudCount', '')
                    print(f'- 膨胀云朵领取成功: {cloudCount}朵')
        else:
            print('-上月未备份，本月无膨胀云朵领取')

    # #  开启备份
    # def open_backup(self):

    # 通知云朵
    @catch_errors
    def open_send(self):
        # 使用新的域名
        send_url = 'https://m.mcloud.139.com/market/msgPushOn/task/status'
        send_data = self.send_request(send_url, headers = self.jwtHeaders).json()

        pushOn = send_data.get('result', {}).get('pushOn', '')  # 0未开启，1开启，2未领取，3已领取
        firstTaskStatus = send_data.get('result', {}).get('firstTaskStatus', '')
        secondTaskStatus = send_data.get('result', {}).get('secondTaskStatus', '')
        onDuaration = send_data.get('result', {}).get('onDuaration', '')  # 开启时间

        if pushOn == 1:
            reward_url = 'https://m.mcloud.139.com/market/msgPushOn/task/obtain'

            if firstTaskStatus == 3:
                print('- 任务1奖励已领取')
            else:
                # 领取任务1
                print('- 领取任务1奖励')
                reward1_data = self.send_request(reward_url, headers = self.jwtHeaders, data = {"type": 1},
                                                 method = "POST").json()
                print(reward1_data.get('result', {}).get('description', ''))

            if secondTaskStatus == 2:
                # 领取任务2
                print('- 领取任务2奖励')
                reward2_data = self.send_request(reward_url, headers = self.jwtHeaders, data = {"type": 2},
                                                 method = "POST").json()
                print(reward2_data.get('result', {}).get('description', ''))

            print(f'- 通知已开启天数: {onDuaration}, 满31天可领取奖励')
        else:
            print('- 通知权限未开启')

    # 分享文件
    def share_file(self):
        """分享最近上传的文件以完成分享任务。"""
        base_header = {
            "authorization": self.Authorization,
            "User-Agent": ua,
            "Accept": "*/*",
            "content-type": "application/json"
        }

        # 获取文件列表（个人云盘新版接口）
        list_url = 'https://yun.139.com/orchestration/personalCloud/catalog/v1.0/getDisk'
        payload_list = {
            "catalogID": self.upload_dir_id,
            "sortDirection": 1,
            "startNumber": 1,
            "endNumber": 100,
            "filterType": 0,
            "catalogSortType": 0,
            "contentSortType": 0,
            "commonAccountInfo": {
                "account": self.account,
                "accountType": 1
            }
        }

        try:
            list_resp = self.session.post(list_url, headers=base_header, data=json.dumps(payload_list))
            list_json = list_resp.json()
            content_list = list_json.get('data', {}).get('getDiskResult', {}).get('contentList', [])
            if not content_list:
                print('❌分享失败: 云盘目录中无文件')
                return False

            # 选择第一个文件或匹配文件名
            share_target = content_list[0]
            for item in content_list:
                if self.share_filename in item.get('contentName', ''):
                    share_target = item
                    break

            file_id = share_target.get('contentID')
            if not file_id:
                print('❌分享失败: 未获取到文件ID')
                return False

            share_url = 'https://yun.139.com/orchestration/personalCloud-rebuild/outlink/v1.0/getOutLink'
            payload_share = {
                "getOutLinkReq": {
                    "subLinkType": 0,
                    "encrypt": 1,
                    "coIDLst": [file_id],
                    "caIDLst": [],
                    "pubType": 1,
                    "dedicatedName": share_target.get('contentName'),
                    "periodUnit": 1,
                    "viewerLst": [],
                    "extInfo": {"isWatermark": 0, "shareChannel": "3001"},
                    "period": 1,
                    "commonAccountInfo": {"account": self.account, "accountType": 1}
                }
            }

            share_resp = self.session.post(share_url, headers=base_header, data=json.dumps(payload_share))
            share_json = share_resp.json()
            if share_json.get('success'):
                out_link = share_json.get('data', {}).get('getOutLinkRes', {}).get('getOutLinkResSet', [{}])[0].get('linkUrl', '')
                print(f'✅分享成功: {out_link}')
                return True
            else:
                print(f"❌分享失败: {share_json.get('message', '未知错误')}")
        except Exception as e:
            print(f'❌分享异常: {e}')
        return False

    def get_redeemable_reward_list(self):
        """获取可兑换奖励列表"""
        url = "https://mrp.mcloud.139.com/market/signin/page/exchangeList?client=app&clientVersion=11.4.4"
        try:
            response = self.send_request(url, headers=self.jwtHeaders, cookies=self.cookies)
            if response and response.status_code == 200:
                reward_data = response.json()
                if reward_data.get("msg") == "success":
                    reward_type_datas = reward_data.get("result", {})
                    reward_list = []
                    print("----------可兑换奖励列表----------")
                    for reward_type_key in reward_type_datas:
                        for reward_item in reward_type_datas.get(reward_type_key, []):
                            oid = reward_item.get("oid")
                            prize_name = reward_item.get('prizeName')
                            p_order = reward_item.get('pOrder')
                            can_redeem = '是' if reward_item.get('dailyRemainderCount') != 0 else '否'
                            msg = f"- {prize_name} | 所需云朵: {p_order} | 可兑换: {can_redeem}"
                            print(msg)
                            reward_list.append({"oid": oid, "prizeName": prize_name})
                    print("---------------------------------")
                    return reward_list
                else:
                    print(f"❌获取可兑换奖励列表失败: {response.text}")
            else:
                print(f"❌获取可兑换奖励列表请求失败: {response.status_code if response else 'N/A'}")
        except Exception as e:
            print(f"❌获取可兑换奖励列表异常: {e}")
        return None

    def redeem_reward(self, oid):
        """兑换奖励"""
        global redeem_message
        url = f"https://mrp.mcloud.139.com/market/signin/page/exchange?prizeId={oid}&client=app&clientVersion=11.4.4&smsCode"
        try:
            response = self.send_request(url, headers=self.jwtHeaders, cookies=self.cookies)
            if response and response.status_code == 200:
                reward_data = response.json()
                msg = reward_data.get('msg', '未知响应')
                if reward_data.get("code") == 0:
                    redeem_message = f"✅兑换成功: {msg}"
                    print(redeem_message)
                else:
                    redeem_message = f"❌兑换失败: {msg}"
                    print(redeem_message)
            else:
                redeem_message = f"❌兑换请求失败: {response.status_code if response else 'N/A'}"
                print(redeem_message)
        except Exception as e:
            redeem_message = f"❌兑换奖励异常: {e}"
            print(redeem_message)


if __name__ == "__main__":
    env_name = 'ydypCK'
    token = os.getenv(env_name)
    if not token:
        print(f'⛔️未获取到ck变量：请检查变量 {env_name} 是否填写')
        exit(0)
    
    IS_REDEEM = False  # 暂时关闭兑换功能
    REDEEM_REWARD_DESCRIPTION = "哔哩哔哩会员月卡"


    cookies = re.split(r'[@\n]', token)
    print(f"移动硬盘共获取到{len(cookies)}个账号")

    for i, account_info in enumerate(cookies, start = 1):
        print(f"\n======== ▷ 第 {i} 个账号 ◁ ========")
        user_instance = YP(account_info)
        user_instance.run()
        if not user_instance.account_valid:
            err_accounts += f'{user_instance.encrypt_account}\n'
        print("\n随机等待5-10s进行下一个账号")
        time.sleep(random.randint(5, 10))

    # 输出异常账号信息
    if err_accounts != '':
        print(f"\n失效账号:\n{err_accounts}")
    else:
        print('当前所有账号ck有效')
    if err_message != '':
        print(f'-错误信息: \n{err_message}')
    print(user_amount)
    
    # 在load_send中获取导入的send函数
    send = load_send()

    # 判断send是否可用再进行调用
    if send:
        msg = []
        if err_accounts:
            msg.append(f"💔失效账号:\n{err_accounts}")
        if err_message:
            msg.append(f"🐞运行异常:\n{err_message}")
        if user_amount:
            msg.append(f"☁️云朵数量:\n{user_amount}")
        if redeem_message:
            msg.append(f"🎁兑换结果:\n{redeem_message}")
            
        if msg:  # 只有在有内容需要通知时才发送
            send('🇨🇳中国移动云盘', '\n'.join(msg))
    else:
        print('通知服务不可用')