'''
name: NodeSeek 签到
cron: 25 8 * * *
'''
import os
import time
from datetime import datetime, timedelta
try:
    from notify import send
except ImportError:
    print("未找到 notify.py，无法发送通知")
    def send(title, content):
        print(f"标题: {title}\n内容: {content}")

try:
    # NodeSeek 的 API 使用了 JA3 指纹验证，普通 requests 库无法通过
    # 需要使用 curl_cffi 来模拟浏览器指纹
    # pip install curl_cffi
    from curl_cffi import requests
except ImportError:
    print("请先安装 curl_cffi 模块: pip install curl_cffi")
    exit(1)

# ----------------- CONFIG -----------------

# 从环境变量中获取 NODESEEK_COOKIE
NODESEEK_COOKIE = os.environ.get("NODESEEK_COOKIE")

# ----------------- FUNC -----------------

def get_signin_stats(session):
    """查询本月的签到收益统计"""
    try:
        # 使用UTC+8时区
        utc_offset = timedelta(hours=8)
        now_utc = datetime.utcnow()
        now_shanghai = now_utc + utc_offset
        current_month_start = datetime(now_shanghai.year, now_shanghai.month, 1)
        
        all_records = []
        page = 1
        
        # 最多查询10页，防止无限循环
        while page <= 10:
            url = f"https://www.nodeseek.com/api/account/credit/page-{page}"
            response = session.get(url, impersonate="chrome110")
            data = response.json()
            
            if not data.get("success") or not data.get("data"):
                break
                
            records = data.get("data", [])
            if not records:
                break
            
            all_records.extend(records)
            
            # 检查最后一条记录的时间，如果超出本月范围就停止
            last_record_time = datetime.fromisoformat(records[-1][3].replace('Z', '+00:00'))
            last_record_time_shanghai = last_record_time.replace(tzinfo=None) + utc_offset
            if last_record_time_shanghai < current_month_start:
                break
            
            page += 1
            time.sleep(0.5)
        
        # 筛选本月签到收益记录
        signin_records = [
            r for r in all_records 
            if "签到收益" in r[2] and 
            datetime.fromisoformat(r[3].replace('Z', '+00:00')).replace(tzinfo=None) + utc_offset >= current_month_start
        ]
        
        if not signin_records:
            return "本月无签到记录"
        
        total_amount = sum(r[0] for r in signin_records)
        days_count = len(signin_records)
        average = round(total_amount / days_count, 2) if days_count > 0 else 0
        
        return (f"{now_shanghai.strftime('%Y年%m月')}已签到 {days_count} 天\n"
                f"共获得 {total_amount} 鸡腿, 平均 {average} 个/天")
        
    except Exception as e:
        return f"查询签到统计异常: {e}"


def sign():
    if not NODESEEK_COOKIE:
        return "Nodeseek Cookie 未设置"

    # 支持多账号，用 & 分隔
    cookies = NODESEEK_COOKIE.split('&')
    msg_list = []

    for i, cookie in enumerate(cookies):
        if not cookie.strip():
            continue

        session = requests.Session()
        session.headers.update({
            'User-Agent': "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
            'origin': "https://www.nodeseek.com",
            'referer': "https://www.nodeseek.com/board",
            'Cookie': cookie.strip()
        })
        
        account_info = f"账号 {i+1}"
        
        try:
            # 获取用户名
            user_info_url = "https://www.nodeseek.com/api/user"
            user_resp = session.get(user_info_url, impersonate="chrome110")
            if user_resp.status_code == 200 and user_resp.json().get('success'):
                username = user_resp.json().get('data', {}).get('username', f"账号 {i+1}")
                account_info = f"账号: {username}"
            
            # 签到
            sign_url = "https://www.nodeseek.com/api/attendance?random=true"
            response = session.post(sign_url, impersonate="chrome110")
            data = response.json()
            
            message = data.get("message", "未能获取消息")
            
            if "鸡腿" in message or data.get("success"):
                status = "签到成功"
            elif "已完成签到" in message:
                status = "今日已签"
            else:
                status = "签到失败"
                
            stats_msg = get_signin_stats(session)
            
            full_message = (f"{account_info}\n"
                            f"签到状态: {status} - {message}\n"
                            f"统计信息: {stats_msg}")
            msg_list.append(full_message)

        except Exception as e:
            msg_list.append(f"{account_info}\n操作异常: {e}")

    return "\n\n".join(msg_list)

# ----------------- MAIN -----------------

if __name__ == '__main__':
    result = sign()
    print("签到结果:")
    print(result)
    send("NodeSeek 签到通知", result) 