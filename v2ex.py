'''
author: https://github.com/Sitoi/dailycheckin/blob/main/dailycheckin/v2ex/main.py
name: v2ex签到
cron: 33 23 7 * * *
 - 名称：V2EX_COOKIE=xxx
update:
    2025.06.21 First commit
    2025.07.06 重构签到验证逻辑，通过检查余额页面的日期记录来确认签到状态，修复了因前端变更导致的误判问题。
    2025.07.20 修复 URL 和 Chrome 版本号，适配最新的 V2EX 网站结构
    2025.07.27 根据最新抓包记录优化请求头，更新 Chrome 版本号和 User-Agent，增强反检测能力
'''
import cloudscraper
import os
import re
from notify import send
import time

# 从环境变量中获取 V2EX_COOKIE
v2ex_cookies = os.environ.get("V2EX_COOKIE")
if v2ex_cookies is None:
    print("V2EX_COOKIE 未设置")
    exit(1)

# 将 cookie 字符串转换为字典
def cookie_to_dict(cookie):
    cookie_dict = {}
    items = cookie.split(';')
    for item in items:
        if '=' in item:
            key, value = item.split('=', 1)
            cookie_dict[key.strip()] = value.strip()
    return cookie_dict

def sign():
    s = cloudscraper.create_scraper()
    s.headers.update({
        'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'accept-language': 'zh-CN,zh;q=0.9',
        'cache-control': 'no-cache',
        'pragma': 'no-cache',
        'priority': 'u=0, i',
        'referer': 'https://v2ex.com/',
        'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138"',
        'sec-ch-ua-arch': '"arm"',
        'sec-ch-ua-bitness': '"64"',
        'sec-ch-ua-full-version': '"138.0.7204.169"',
        'sec-ch-ua-full-version-list': '"Not)A;Brand";v="*******", "Chromium";v="138.0.7204.169"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-model': '""',
        'sec-ch-ua-platform': '"macOS"',
        'sec-ch-ua-platform-version': '"15.5.0"',
        'sec-fetch-dest': 'document',
        'sec-fetch-mode': 'navigate',
        'sec-fetch-site': 'same-origin',
        'sec-fetch-user': '?1',
        'upgrade-insecure-requests': '1',
        'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/13******* Safari/537.36',
    })
    
    cookie_dict = cookie_to_dict(v2ex_cookies)
    s.cookies.update(cookie_dict)
    
    # 访问每日任务页面
    try:
        r = s.get('https://v2ex.com/mission/daily', timeout=10)
        r.raise_for_status()
    except Exception as e:
        return f"访问每日任务页面失败: {e}"

    # 寻找签到链接
    sign_in_url_match = re.search(r'/mission/daily/redeem\?once=\d+', r.text)
    
    if not sign_in_url_match:
        # 如果没有找到签到链接，可能是已经签到过了
        if 'Daily bonus has been redeemed' in r.text or '每日登录奖励已领取' in r.text:
            return get_check_in_info(s, signed_in_already=True, daily_page_content=r.text)
        else:
            # 找不到签到链接，也找不到已签到信息，尝试检查余额页面确认
            return get_check_in_info(s, signed_in_already=True, daily_page_content=r.text)

    sign_in_url = "https://v2ex.com" + sign_in_url_match.group(0)

    # 进行签到
    try:
        # 更新签到请求的 referer
        s.headers.update({'referer': 'https://v2ex.com/mission/daily'})
        r_signin = s.get(sign_in_url, timeout=10)
        r_signin.raise_for_status()

        # 签到成功后，V2EX会重定向到 /mission/daily 页面，并显示"每日登录奖励已领取"
        if '每日登录奖励已领取' not in r_signin.text:
            if 'Verifying you are human' in r_signin.text or 'captcha' in r_signin.text.lower():
                return "签到失败：Cloudflare 人机验证失败，无法自动签到。请尝试更新 Cookie 或手动签到一次。"

            # 尝试从页面中提取错误信息
            error_match = re.search(r'<div class="problem">(.*?)</div>', r_signin.text, re.DOTALL)
            if error_match:
                error_message = re.sub(r'<[^>]+>', '', error_match.group(1)).strip()
                return f"签到失败：{error_message}"

            return "签到失败：签到后未发现成功标识。可能是 V2EX 页面结构已更新。"

        time.sleep(2) # 等待一下，确保信息同步
    except Exception as e:
        return f"签到请求失败: {e}"

    # 签到后获取信息
    return get_check_in_info(s, daily_page_content=r_signin.text)

def get_check_in_info(session, signed_in_already=False, daily_page_content=None):
    # 访问余额页面
    try:
        r = session.get('https://v2ex.com/balance', timeout=10)
        r.raise_for_status()
    except Exception as e:
        return f"访问余额页面失败: {e}"

    if '请先登录' in r.text:
        return "无法获取信息：Cookie 已失效或未提供。"

    if signed_in_already:
        # 检查今天是否真的已经领取了奖励
        today_date = time.strftime("%Y-%m-%d")
        today_reward_match = re.search(f'{today_date}.*?每日登录奖励.*?<span class="positive"><strong>(.*?)</strong></span>', r.text, re.DOTALL)
        if today_reward_match:
            today_reward = f"已领取 {today_reward_match.group(1).strip()} 铜币"
        else:
            return "签到失败：无法找到签到链接，且未在余额页面找到今日签到记录。请检查 Cookie 或手动签到。"
    else:
        balance_match = re.search(r'获得 (.*?) 的每日登录奖励', r.text)
        today_reward = balance_match.group(1) if balance_match else "奖励信息获取失败"
    
    # Limit search to the top part of the page before the transaction log
    main_content = r.text
    transaction_log_marker = 'id="balance-log"'
    if transaction_log_marker in main_content:
        main_content = main_content.split(transaction_log_marker, 1)[0]
        
    total_balance_match = re.search(r'当前账户余额.*?<strong>(.*?)</strong>', main_content, re.DOTALL)
    if total_balance_match:
        balance_html = total_balance_match.group(1)
        # Replace html tags with spaces for readability
        balance_text = re.sub(r'<[^>]+>', ' ', balance_html)
        # Consolidate whitespace
        total_balance = ' '.join(balance_text.split())
    else:
        total_balance = "获取失败"


    # 访问每日任务页面获取连续签到天数
    if daily_page_content is None:
        try:
            r_daily = session.get('https://v2ex.com/mission/daily', timeout=10)
            r_daily.raise_for_status()
            daily_page_content = r_daily.text
        except Exception as e:
            daily_page_content = ""

    if daily_page_content:
        days_match = re.search(r'已连续登录 (\d+) 天', daily_page_content)
        consecutive_days = days_match.group(1) if days_match else "获取失败"

        username_match = re.search(r'<a href="/member/(\w+)" class="top">', daily_page_content)
        username = username_match.group(1) if username_match else "获取失败"
    else:
        consecutive_days = "获取失败"
        username = "获取失败"

    message = (
        f"帐号信息: {username}\n"
        f"今日签到奖励: {today_reward}\n"
        f"帐号余额: {total_balance}\n"
        f"连续签到天数: {consecutive_days} 天"
    )
    return message


if __name__ == '__main__':
    result = sign()
    print(result)
    send("V2EX 签到", result)
