#!/usr/bin/env python3
"""
夸克农场签名算法分析工具
"""
import hashlib
import hmac
import time
import json
import re
from datetime import datetime

class SignatureAnalyzer:
    def __init__(self):
        self.known_signatures = {
            'tomorrow_gift': {
                'old': '0003c4bb4fa2a0261dc003827cf4b24d7ccee00fb3bd',
                'new': '0003590edb0da679981cfc3b105e20e8e60d2af428f7',
                'params': {
                    'taskType': 'TOMORROW_GIFT',
                    'salt': '********************************',
                    'appId': 'quark_baba_farm_task'
                }
            },
            'sign_in': {
                'old': '00035361aa8461c93a857b585b86517c6c2eea393037',
                'new': '00036572d579b18fd8d4a1951ec6620e86af1755ceba',
                'params': {
                    'appId': 'quark_baba_farm_task'
                }
            },
            'watering_reward': {
                'new': '00030d5c6a17e92d6ba38968507faa56171dfb2c0986',
                'params': {
                    'taskType': 'MULTI_OPEN_GIFT',
                    'salt': '********************************',
                    'appId': 'quark_baba_farm_task'
                }
            }
        }
        
    def analyze_signature_pattern(self):
        """分析签名模式"""
        print("🔍 签名模式分析")
        print("=" * 50)
        
        for name, data in self.known_signatures.items():
            print(f"\n📋 {name}:")
            if 'old' in data:
                old_sig = data['old']
                new_sig = data['new']
                print(f"  旧签名: {old_sig}")
                print(f"  新签名: {new_sig}")
                
                # 分析差异
                diff_positions = []
                for i, (old_char, new_char) in enumerate(zip(old_sig, new_sig)):
                    if old_char != new_char:
                        diff_positions.append(i)
                
                print(f"  差异位置: {diff_positions}")
                print(f"  差异字符数: {len(diff_positions)}")
                
                # 分析前缀
                common_prefix = ""
                for i, (old_char, new_char) in enumerate(zip(old_sig, new_sig)):
                    if old_char == new_char:
                        common_prefix += old_char
                    else:
                        break
                print(f"  公共前缀: {common_prefix} (长度: {len(common_prefix)})")
            else:
                print(f"  签名: {data['new']}")
    
    def test_common_hash_algorithms(self, input_data):
        """测试常见哈希算法"""
        algorithms = {
            'md5': hashlib.md5,
            'sha1': hashlib.sha1,
            'sha256': hashlib.sha256,
            'sha512': hashlib.sha512
        }
        
        results = {}
        for name, algo in algorithms.items():
            hash_obj = algo(input_data.encode('utf-8'))
            results[name] = hash_obj.hexdigest()
        
        return results
    
    def test_hmac_algorithms(self, input_data, key):
        """测试HMAC算法"""
        algorithms = {
            'hmac_md5': hashlib.md5,
            'hmac_sha1': hashlib.sha1,
            'hmac_sha256': hashlib.sha256,
            'hmac_sha512': hashlib.sha512
        }
        
        results = {}
        for name, algo in algorithms.items():
            hmac_obj = hmac.new(key.encode('utf-8'), input_data.encode('utf-8'), algo)
            results[name] = hmac_obj.hexdigest()
        
        return results
    
    def generate_possible_inputs(self, params, timestamp=None):
        """生成可能的签名输入"""
        if timestamp is None:
            timestamp = int(time.time() * 1000)
        
        # 常见的签名输入组合方式
        combinations = []
        
        # 方式1: 按字母顺序排列参数
        sorted_params = sorted(params.items())
        combo1 = '&'.join([f"{k}={v}" for k, v in sorted_params])
        combinations.append(f"sorted_params: {combo1}")
        
        # 方式2: 添加时间戳
        combo2 = combo1 + f"&timestamp={timestamp}"
        combinations.append(f"with_timestamp: {combo2}")
        
        # 方式3: 只用关键参数
        key_params = ['appId', 'taskType', 'salt']
        combo3 = '&'.join([f"{k}={params.get(k, '')}" for k in key_params if k in params])
        combinations.append(f"key_params: {combo3}")
        
        # 方式4: JSON格式
        combo4 = json.dumps(params, sort_keys=True, separators=(',', ':'))
        combinations.append(f"json_format: {combo4}")
        
        return combinations
    
    def attempt_signature_reverse(self):
        """尝试逆向签名算法"""
        print("\n🔬 尝试逆向签名算法")
        print("=" * 50)
        
        for name, data in self.known_signatures.items():
            if 'new' not in data:
                continue
                
            print(f"\n📋 分析 {name}:")
            target_signature = data['new']
            params = data['params']
            
            # 生成可能的输入
            possible_inputs = self.generate_possible_inputs(params)
            
            # 测试各种算法
            for input_desc in possible_inputs:
                input_data = input_desc.split(': ', 1)[1]
                
                # 测试普通哈希
                hash_results = self.test_common_hash_algorithms(input_data)
                
                # 测试HMAC (使用常见密钥)
                common_keys = ['quark', 'farm', 'baba', 'secret', 'key', params.get('salt', '')]
                
                for key in common_keys:
                    if key:
                        hmac_results = self.test_hmac_algorithms(input_data, key)
                        
                        # 检查是否匹配
                        for algo_name, result in {**hash_results, **hmac_results}.items():
                            # 检查完整匹配
                            if result == target_signature:
                                print(f"  ✅ 完全匹配! {algo_name}")
                                print(f"     输入: {input_desc}")
                                print(f"     密钥: {key if 'hmac' in algo_name else 'N/A'}")
                                return
                            
                            # 检查部分匹配（去掉前缀）
                            if result == target_signature[4:]:
                                print(f"  🔍 部分匹配 (去掉0003前缀): {algo_name}")
                                print(f"     输入: {input_desc}")
                                print(f"     密钥: {key if 'hmac' in algo_name else 'N/A'}")
                            
                            # 检查前缀匹配
                            if target_signature.startswith(result[:8]):
                                print(f"  🔍 前缀匹配: {algo_name}")
                                print(f"     输入: {input_desc}")
    
    def analyze_signature_timing(self):
        """分析签名时效性"""
        print("\n⏰ 签名时效性分析")
        print("=" * 50)
        
        # 基于抓包时间分析
        print("📅 已知签名时间线:")
        print("  - 2025.07.27: 旧签名失效")
        print("  - 2025.07.28: 新签名生效")
        print("  - 估计有效期: 1-7天")
        
        print("\n🔄 建议更新策略:")
        print("  1. 每日检测签名有效性")
        print("  2. 失效时自动降级到只读模式")
        print("  3. 提供签名更新提醒")
        print("  4. 维护签名历史记录")

def main():
    analyzer = SignatureAnalyzer()
    
    print("🔐 夸克农场签名算法分析")
    print("=" * 60)
    
    # 1. 分析签名模式
    analyzer.analyze_signature_pattern()
    
    # 2. 尝试逆向算法
    analyzer.attempt_signature_reverse()
    
    # 3. 分析时效性
    analyzer.analyze_signature_timing()
    
    print("\n" + "=" * 60)
    print("📊 分析完成")

if __name__ == '__main__':
    main()
