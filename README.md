# QLScripts - 青龙面板自动化签到脚本集合

这是一个包含多种自动化签到脚本的项目，支持 Python 和 Node.js 混合开发，使用 UV 进行 Python 依赖管理。

## 🚀 快速开始

### 前置要求

- Python 3.8+
- Node.js 14+
- UV (Python 包管理器)
- npm (Node.js 包管理器)

### 安装和设置

1. **克隆项目**
   ```bash
   git clone <your-repo-url>
   cd qlscripts
   ```

2. **安装依赖**
   ```bash
   # 创建 Python 虚拟环境
   uv venv

   # 安装 Python 依赖
   uv pip install -r requirements.txt

   # 安装 Node.js 依赖
   npm install
   ```

3. **激活环境**
   ```bash
   source .venv/bin/activate
   ```

## 📁 项目结构

```
qlscripts/
├── .venv/                  # UV 创建的 Python 虚拟环境
├── node_modules/           # npm 安装的 Node.js 依赖
├── qlscripts_backup_*/     # 备份的 conda 环境
├──
├── Python 脚本/
│   ├── SPDailyCheckUp.py   # South-plus 签到
│   ├── nodeseek.py         # NodeSeek 签到
│   ├── v2ex.py            # V2EX 签到
│   ├── quark_farm.py      # 夸克农场任务
│   ├── caiyun.py          # 移动云盘任务
│   └── notify.py          # 通知模块
├──
├── JavaScript 脚本/
│   ├── ql.js              # 青龙面板 API
│   ├── common.js          # 通用函数
│   └── sendNotify1.js     # 通知发送
└──
└── 项目配置/
    ├── pyproject.toml     # Python 项目配置
    ├── package.json       # Node.js 项目配置
    ├── requirements.txt   # Python 依赖
    ├── README.md          # 项目说明
    ├── USAGE.md           # 使用指南
    └── .gitignore         # Git 忽略文件
```

## 🐍 Python 脚本使用

### 激活环境

```bash
source .venv/bin/activate
```

### 运行脚本

```bash
# South-plus 签到
python SPDailyCheckUp.py

# NodeSeek 签到
python nodeseek.py

# V2EX 签到
python v2ex.py

# 夸克农场任务
python quark_farm.py
```

## 🟨 Node.js 脚本使用

```bash
# 青龙面板相关
node ql.js
```

## 🔧 依赖管理

### Python 依赖 (使用 UV)

```bash
# 安装新依赖
uv pip install package_name

# 更新依赖
uv pip install --upgrade package_name

# 查看已安装的包
uv pip list

# 生成 requirements.txt
uv pip freeze > requirements.txt
```

### Node.js 依赖 (使用 npm)

```bash
# 安装新依赖
npm install package_name

# 更新依赖
npm update

# 查看已安装的包
npm list
```

## 🌟 UV 的优势

相比 conda，UV 具有以下优势：

1. **速度更快** - 依赖解析和安装速度显著提升
2. **体积更小** - 不需要庞大的 conda 环境
3. **更简洁** - 专注于 Python 包管理，没有多余功能
4. **兼容性好** - 完全兼容 pip 和 PyPI
5. **现代化** - 使用 Rust 编写，性能优异

## 📝 环境变量配置

各脚本需要的环境变量请参考各脚本文件头部的注释说明。

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

MIT License
