"""
author: leojun
name: 夸克农场任务
cron: 10 0 * * *
配置变量:
    - QUARK_FARM_DATA: 包含 kps 和 headers 的 JSON 字符串。
      格式: {"kps": "...", "headers": {"User-Agent": "...", ...}}
update:
    2025.07.06 初始化脚本，根据抓包数据实现浇水功能，并遵循项目编码规范。
    2025.07.06 重构配置逻辑，改为从单一环境变量读取，移除 config.json 依赖。
    2025.07.12 根据新的抓包记录优化：
        - 更新版本号至 7.14.0.870
        - 实现视频任务的领奖功能（通过 triggerTask 接口）
        - 改进任务处理逻辑，尝试自动领取视频任务奖励
    2025.07.13 根据最新抓包记录优化：
        - 发现并实现签到执行接口 triggerSignIn
        - 优化签到流程：先查询状态，再执行签到
        - 签到现在可以正确返回成功/失败状态
        - 发现任务有 complete 和 award 两种类型
        - 优化视频任务处理：先发送 complete 请求，再发送 award 请求
        - 更新最新的 taskId 到 sign 映射
    2025.07.26 根据新抓包记录添加浇水奖励领取功能：
        - 实现 drawCustomTaskAward 接口，用于领取每N次浇水后的奖励
        - 在浇水循环中检查并自动领取浇水奖励
        - 更新版本号至 7.14.5.880
        - 根据最新签到抓包记录优化签到功能，更新签名
    2025.07.27 优化每日奖励功能：
        - 移除硬编码的「200化肥」描述，改为动态解析奖励内容
        - 新增 _parse_daily_reward_info 方法，支持多种奖励数据结构
        - 改进错误处理，区分已领取和其他错误情况
        - 添加测试脚本验证功能正确性
        - 新增 claim_tomorrow_gift 方法，自动领取每日 tomorrow gift 化肥
        - 在主流程中集成 tomorrow gift 领取功能
    2025.07.28 根据新抓包数据更新签名：
        - 更新 tomorrow gift 签名：0003590edb0da679981cfc3b105e20e8e60d2af428f7
        - 更新签到签名：00036572d579b18fd8d4a1951ec6620e86af1755ceba
        - 更新浇水奖励签名：00030d5c6a17e92d6ba38968507faa56171dfb2c0986
        - 修复签名验证失败问题，确保功能正常工作
"""
import requests
import json
import time
import os
import random
import sys
from uuid import uuid4
from notify import send

# 日志打印
def log(content):
    msg = f'{time.strftime("%Y-%m-%d %H:%M:%S")} {content}'
    print(msg)
    sys.stdout.flush()

class TaskExecutionError(Exception):
    pass

class QuarkFarm:
    # 将非敏感、基本不变的参数硬编码
    URL_PARAMS_TEMPLATE = {
        "uc_param_str": "dnfrpfbivessbtbmnilauputogpintnwmtsvcppcprsnnnchmicckpos",
        "dn": "62121825056-f57b971f",
        "fr": "android",
        "pf": "3300",
        "bi": "35825",
        "ve": "7.14.5.880",
        "ss": "400x851",
        "ni": "bTkwBFsaS8ITsIcfZpOO2u/mDS5WIyKVtE+HIBjnQWM3bUU=",
        "la": "zh",
        "ut": "AANNfpx6lmz8u2LBe8DfKPt+8z5Ay79UDo4a+Im+Oo8/SQ==",
        "nt": "5",
        "nw": "0",
        "mt": "UeQBKJJLPLqKEgKX3tPFwaXHNTyUQJ9s",
        "sv": "release",
        "pc": "AAQ%2FJDdbUVTj%2FurM6xuCLCy3OYHgL2f9RrOktpNIMf9%2BE96epwuBtQ7Nz%2BGKeHqZi9TV9JuX7CpH3pfnmaQqB4qD",
        "pr": "ucpro",
        "sn": "2408-62121825056-e41cf38b",
        "ch": "kk@store",
        "mi": "23127PN0CC",
        "os": "15",
        "fve": "2.11.0"
    }

    def __init__(self, user_config):
        """
        初始化
        :param user_config: 从环境变量加载的用户特定配置字典
        """
        self.headers = user_config.get('headers', {})
        self.kps = user_config.get('kps')
        
        if not self.kps or not self.headers or 'User-Agent' not in self.headers:
            raise ValueError("环境变量配置不完整，请确保 QUARK_FARM_DATA 包含有效的 kps 和 headers。")

        self.session = requests.Session()
        self.session.headers.update(self.headers)

        # 执行新的初始化流程
        try:
            self._init_session()
        except TaskExecutionError as e:
            # 在初始化阶段就失败，直接抛出 ValueError 影响主逻辑
            raise ValueError(f"会话初始化失败: {e}")

    def _request(self, url, method='POST', payload=None, params_override=None):
        """
        通用的请求方法
        """
        # 准备请求参数
        params = self.URL_PARAMS_TEMPLATE.copy()
        if params_override:
            params.update(params_override)
        params['__t'] = int(time.time() * 1000)
        # kps 也需要加入到 params
        params['kps'] = self.kps
        
        try:
            if method.upper() == 'POST':
                response = self.session.post(url, json=payload, params=params, timeout=20)
            else:
                response = self.session.get(url, params=params, timeout=20)
            
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            raise TaskExecutionError(f"网络请求失败: {e}")
        except json.JSONDecodeError:
            # 增加对空响应的处理
            if not response.text:
                log("警告: 服务器返回空响应。")
                return None
            raise TaskExecutionError(f"响应解析失败: {response.text}")

    def _request_sign_in(self, url, payload):
        """
        专门用于签到的请求方法，确保 kps 只在 JSON payload 中
        """
        # 准备请求参数，不包含 kps
        params = self.URL_PARAMS_TEMPLATE.copy()
        params['__t'] = int(time.time() * 1000)

        try:
            response = self.session.post(url, json=payload, params=params, timeout=20)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            raise TaskExecutionError(f"网络请求失败: {e}")
        except json.JSONDecodeError:
            # 增加对空响应的处理
            if not response.text:
                log("警告: 服务器返回空响应。")
                return None
            raise TaskExecutionError(f"响应解析失败: {response.text}")

    def _init_session(self):
        """
        执行会话初始化，模拟客户端真实流程
        """
        log("【开始会话初始化】")
        try:
            self._get_config()
            self._get_session_token()
            log("✅ 会话初始化成功")
        except TaskExecutionError as e:
            # 初始化失败是致命的，直接抛出异常
            raise TaskExecutionError(f"会话初始化失败: {e}")

    def _get_config(self):
        """
        阶段一：获取远程任务配置
        """
        log("  - 阶段1: 获取远程任务配置...")
        url = "https://coral2.quark.cn/aggregation/task_action"
        params_override = {
            "req_services": '[{"module":"diamond","key":"baba-task-action-config"}]',
            "sceneCode": "quark_baba_farm"
        }
        params = self.URL_PARAMS_TEMPLATE.copy()
        params.update(params_override)
        params['__t'] = int(time.time() * 1000)

        try:
            response = self.session.get(url, params=params, timeout=20)
            response.raise_for_status()
            log("  - 阶段1: 获取配置成功。")
        except requests.exceptions.RequestException as e:
            raise TaskExecutionError(f"获取远程配置失败: {e}")

    def _get_session_token(self):
        """
        阶段二：获取会话 ctoken
        """
        log("  - 阶段2: 获取会话 ctoken...")
        url = "https://b.quark.cn/api/v1/ssr/async-fetch/quark_baba_farm/index"
        params = self.URL_PARAMS_TEMPLATE.copy()
        params['__t'] = int(time.time() * 1000)
        
        try:
            response = self.session.get(url, params=params, timeout=20)
            response.raise_for_status()
            if 'ctoken' in self.session.cookies:
                log(f"  - 阶段2: 成功获取 ctoken: {self.session.cookies['ctoken']}")
            else:
                log("  - 阶段2: 响应中未找到 ctoken，但请求成功。")
        except requests.exceptions.RequestException as e:
            raise TaskExecutionError(f"获取 ctoken 失败: {e}")

    def water(self):
        """
        浇水任务 (修正：永远只浇水一次)
        """
        url = "https://farm-server.quark.cn/watering"
        
        payload = {
            "appId": "quark_baba_farm_task",
            "kps": self.kps,
            "requestId": str(uuid4()),
            "waterTimes": 1 # 修正：永远只发送1
        }
        
        result = self._request(url, payload=payload)
        
        if result.get('code') == 'OK' and result.get('success'):
            # 成功的日志由主循环控制，这里保持安静
            return result['data']
        else:
            error_msg = result.get('message', '未知错误')
            log(f"浇水失败: {error_msg}")
            raise TaskExecutionError(f"浇水失败: {error_msg}")

    def draw_watering_reward(self):
        """
        领取浇水奖励 (每N次浇水后的奖励)
        """
        url = "https://farm-server.quark.cn/task/drawCustomTaskAward"

        payload = {
            "kps": self.kps,
            "appId": "quark_baba_farm_task",
            "requestId": str(uuid4()),
            "taskType": "MULTI_OPEN_GIFT",
            "salt": "********************************",
            "sign": "00030d5c6a17e92d6ba38968507faa56171dfb2c0986"  # 2025.07.28 更新的签名
        }

        result = self._request(url, payload=payload)

        if result.get('code') == 'OK' and result.get('success'):
            # 检查是否有奖励数据
            data = result.get('data', {})
            if data:
                # 尝试解析奖励信息
                prizes = data.get('prizes', [])
                if prizes:
                    reward_items = []
                    for prize in prizes:
                        reward = prize.get('rewardItem', {})
                        amount = reward.get('amount', 0)
                        name = reward.get('name', '奖励')
                        reward_items.append(f"{name}x{amount}")
                    log(f"🎁 浇水奖励领取成功！获得: {', '.join(reward_items)}")
                else:
                    log("🎁 浇水奖励领取成功！")
                return True
            else:
                log("🎁 浇水奖励领取成功！")
                return True
        else:
            error_msg = result.get('message', '未知错误')
            # 如果是没有可领取的奖励，不算错误
            if 'no reward' in error_msg.lower() or '没有奖励' in error_msg or 'not available' in error_msg.lower():
                log("🎁 当前没有可领取的浇水奖励")
                return False
            else:
                log(f"🎁 浇水奖励领取失败: {error_msg}")
                return False

    def claim_tomorrow_gift(self):
        """
        领取每日 tomorrow gift 化肥

        功能说明：
        - tomorrow gift 是夸克农场的每日化肥奖励，通常在每日 00:00 开始可领取
        - 每次浇水会增加 tomorrow gift 的奖励数量（每次+100肥料）
        - 状态包括：ADVANCE_NOTICE(预告)、AVAILABLE(可领取)、INIT(可领取)等
        - 会自动检查今日是否已领取，避免重复操作
        - 使用 drawCustomTaskAward 接口，taskType 为 "TOMORROW_GIFT"

        注意：当前使用固定签名，如果签名失效需要重新抓包更新
        """
        log("【检查每日 tomorrow gift 化肥状态】")

        # 先通过 home 接口检查 tomorrow gift 状态
        try:
            home_info = self.get_home_info()
            tomorrow_gift_info = home_info.get('activityData', {}).get('tomorrowGift', {})

            if not tomorrow_gift_info:
                log("🎁 未找到 tomorrow gift 活动信息")
                return False

            status = tomorrow_gift_info.get('status', 'UNKNOWN')
            today_received = tomorrow_gift_info.get('todayReceived', False)
            point_amount = tomorrow_gift_info.get('pointAmount', 0)
            receive_start_time = tomorrow_gift_info.get('receiveStartTime', 0)

            log(f"🎁 tomorrow gift 状态: {status}, 今日已领取: {today_received}, 奖励金额: {point_amount}")

            # 如果今日已领取，直接返回
            if today_received:
                log("🎁 tomorrow gift 今日已领取，无需重复操作")
                return False

            # 如果状态不是可领取状态，提供相应提示
            if status == 'ADVANCE_NOTICE':
                import time
                start_time_str = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(receive_start_time/1000))
                log(f"🎁 tomorrow gift 当前为预告状态，领取时间: {start_time_str}")
                return False
            elif status not in ['AVAILABLE', 'INIT', 'CAN_RECEIVE']:
                log(f"🎁 tomorrow gift 当前状态 '{status}' 不可领取")
                return False

            # 状态检查通过，尝试领取
            log("🎁 tomorrow gift 状态检查通过，开始尝试领取...")

        except Exception as e:
            log(f"🎁 检查 tomorrow gift 状态时出错: {e}，尝试直接领取...")

        # 尝试领取 tomorrow gift
        url = "https://farm-server.quark.cn/task/drawCustomTaskAward"
        payload = {
            "kps": self.kps,
            "appId": "quark_baba_farm_task",
            "requestId": str(uuid4()),
            "taskType": "TOMORROW_GIFT",
            "salt": "********************************",
            "sign": "0003590edb0da679981cfc3b105e20e8e60d2af428f7"  # 2025.07.28 更新的签名
        }

        result = self._request(url, payload=payload)

        if result.get('code') == 'OK' and result.get('success'):
            # 检查是否有奖励数据
            data = result.get('data', {})
            if data:
                # 尝试解析奖励信息
                prizes = data.get('prizes', [])
                if prizes:
                    reward_items = []
                    for prize in prizes:
                        reward = prize.get('rewardItem', {})
                        amount = reward.get('amount', 0)
                        name = reward.get('name', '奖励')
                        reward_items.append(f"{name}x{amount}")
                    log(f"🎁 tomorrow gift 领取成功！获得: {', '.join(reward_items)}")
                else:
                    log("🎁 tomorrow gift 领取成功！")
                return True
            else:
                log("🎁 tomorrow gift 领取成功！")
                return True
        else:
            error_code = result.get('code', 'UNKNOWN')
            error_msg = result.get('message', '未知错误')

            # 根据错误码提供更准确的信息
            if error_code == 'REQUEST_SIGN_INVALID':
                log("🎁 tomorrow gift 领取失败: 签名验证失败")
                log("💡 提示: 需要重新抓包获取最新的签名，当前签名已失效")
                log(f"💡 当前状态: {status}, 奖励金额: {point_amount}, 今日未领取")
            elif 'already' in error_msg.lower() or '已领取' in error_msg or 'claimed' in error_msg.lower():
                log("🎁 tomorrow gift 今日已领取")
            elif 'no reward' in error_msg.lower() or '没有奖励' in error_msg or 'not available' in error_msg.lower():
                log("🎁 tomorrow gift 暂无可领取奖励")
            else:
                log(f"🎁 tomorrow gift 领取失败: [{error_code}] {error_msg}")

            return False

    def get_home_info(self):
        """
        获取农场主页信息，检查任务状态
        """
        log("【获取农场主页信息】")
        url = "https://farm-server.quark.cn/home"
        params_override = {
            "appId": "quark_baba_farm_task",
            "requestId": str(uuid4())
        }
        result = self._request(url, method='GET', payload=None, params_override=params_override)
        
        if result.get('code') == 'OK' and result.get('success'):
            log("获取农场信息成功。")
            return result.get('data', {})
        else:
            log(f"服务器返回错误，完整响应: {result}")
            error_msg = result.get('message', '未知错误')
            log(f"获取农场信息失败: {error_msg}")
            raise TaskExecutionError(f"获取农场信息失败: {error_msg}")

    def claim_daily_reward(self):
        """
        领取每日奖励 (动态解析奖励内容)
        """
        log("【尝试领取每日奖励】")
        url = "https://farm-server.quark.cn/home"
        params_override = {
            "appId": "quark_baba_farm_task",
            "requestId": str(uuid4()),
            "entry": "jingxuan" # 关键参数
        }
        result = self._request(url, method='GET', payload=None, params_override=params_override)

        if result.get('code') == 'OK' and result.get('success'):
            data = result.get('data', {})

            # 尝试解析奖励信息
            reward_info = self._parse_daily_reward_info(data)
            if reward_info:
                log(f"🎁 每日奖励领取成功！{reward_info}")
            else:
                log("🎁 每日奖励接口调用成功，服务器已处理。")

            return data
        else:
            error_msg = result.get('message', '未知错误')
            # 检查是否是已经领取的情况
            if 'already' in error_msg.lower() or '已领取' in error_msg or 'claimed' in error_msg.lower():
                log("🎁 每日奖励今日已领取，无需重复操作。")
            else:
                log(f"🎁 领取每日奖励失败: {error_msg}")
            # 这里不抛出异常，因为这个奖励可能已经领过，失败不应中断主流程
            return None

    def _parse_daily_reward_info(self, data):
        """
        解析每日奖励信息，尝试从响应数据中提取奖励详情
        """
        if not data:
            return None

        # 尝试多种可能的奖励信息结构
        reward_info_sources = [
            # 可能的奖励信息位置1: prizes 数组
            data.get('prizes', []),
            # 可能的奖励信息位置2: rewards 数组
            data.get('rewards', []),
            # 可能的奖励信息位置3: activityData 中的奖励信息
            data.get('activityData', {}).get('rewards', []),
            # 可能的奖励信息位置4: gameInfo 中的奖励信息
            data.get('gameInfo', {}).get('rewards', [])
        ]

        for rewards in reward_info_sources:
            if rewards and isinstance(rewards, list):
                reward_items = []
                for reward in rewards:
                    # 尝试不同的奖励项结构
                    reward_item = reward.get('rewardItem', reward)
                    if reward_item:
                        amount = reward_item.get('amount', 0)
                        name = reward_item.get('name', reward_item.get('itemName', '奖励'))
                        if amount > 0:
                            reward_items.append(f"{name}x{amount}")
                        elif name != '奖励':
                            reward_items.append(name)

                if reward_items:
                    return f"获得: {', '.join(reward_items)}"

        # 如果没有找到具体的奖励信息，尝试从用户信息变化中推断
        game_info = data.get('gameInfo', {})
        account_info = game_info.get('accountInfo', {})

        # 检查是否有肥料数量信息
        fertilizer_amount = account_info.get('happyPoint')
        if fertilizer_amount is not None:
            return f"当前肥料: {fertilizer_amount}"

        return None

    def query_task_list(self):
        """
        查询任务列表
        """
        log("【查询任务列表】")
        url = "https://farm-server.quark.cn/task/queryTaskList"
        params_override = {
            "appId": "quark_baba_farm_task",
            "requestId": str(uuid4())
        }
        result = self._request(url, method='GET', payload=None, params_override=params_override)
        
        if result.get('code') == 'OK' and result.get('success'):
            log("查询任务列表成功。")
            # 打印任务状态
            tasks = result.get('data', {}).get('values', [])
            if not tasks:
                log("任务列表为空。")
                return []
            
            log("解析到以下任务:")
            for task in tasks:
                task_name = task.get('name', '未知任务')
                state = task.get('state', -1)
                event = task.get('event', 'no_event')
                status_map = {
                    0: "未完成",
                    1: "已完成,待领取",
                    2: "已领取",
                    7: "进行中" # 根据之前的输出来看，7可能是进行中的复合任务
                }
                log(f"  - 名称: {task_name}, 事件: {event}, 状态: {status_map.get(state, '未知状态')}")
            return tasks
        else:
            error_msg = result.get('message', '未知错误')
            log(f"查询任务列表失败: {error_msg}")
            raise TaskExecutionError(f"查询任务列表失败: {error_msg}")

    def sign_in(self):
        """
        每日签到
        """
        log("【开始执行每日签到】")
        # 首先查询签到状态
        query_url = "https://farm-server.quark.cn/task/querySignIn"
        params_override = {
            "appId": "quark_baba_farm_task",
            "requestId": str(uuid4())
        }
        
        result = self._request(query_url, method='GET', payload=None, params_override=params_override)
        
        if result.get('code') == 'OK' and result.get('success'):
            sign_data = result.get('data', {})
            # 检查今日是否已完成签到
            today_completed = sign_data.get('todayCompleted', False)
            if today_completed:
                log("今日已签到，无需重复操作。")
                return None
            
            # 找到今天要签到的任务
            item_list = sign_data.get('itemList', [])
            current_task = None
            for item in item_list:
                if item.get('signInState') == 'START':
                    current_task = item.get('curTask', {})
                    break
            
            if not current_task:
                log("未找到可签到的任务。")
                return None
            
            # 执行签到
            log(f"准备签到第{current_task.get('name', '')}...")
            trigger_url = "https://farm-server.quark.cn/task/triggerSignIn"
            payload = {
                "kps": self.kps,
                "appId": "quark_baba_farm_task",
                "requestId": str(uuid4()),
                "sign": "00036572d579b18fd8d4a1951ec6620e86af1755ceba"  # 2025.07.28 更新的签名
            }
            
            # 对于签到请求，需要特殊处理，kps 不应该在 URL 参数中重复
            trigger_result = self._request_sign_in(trigger_url, payload)
            
            if trigger_result and trigger_result.get('success'):
                # 获取奖励信息
                prizes = trigger_result.get('data', {}).get('prizes', [])
                if prizes:
                    reward = prizes[0].get('rewardItem', {})
                    amount = reward.get('amount', 0)
                    log(f"签到成功！获得{reward.get('name', '奖励')}: {amount}")
                else:
                    log("签到成功！")
                return trigger_result.get('data')
            else:
                error_code = trigger_result.get('code', 'UNKNOWN') if trigger_result else 'NO_RESPONSE'
                error_msg = trigger_result.get('message', '未知错误') if trigger_result else '无响应'
                log(f"签到执行失败: [{error_code}] {error_msg}")
                if error_code == 'REQUEST_SIGN_INVALID':
                    log("💡 提示: 签名验证失败，需要重新抓包获取最新的签名")
                    log(f"💡 当前可签到: 第{current_task.get('name', '')}，奖励: {current_task.get('rewardItems', [{}])[0].get('name', '未知')}")
                elif 'sign' in error_msg.lower():
                    log("💡 提示: 签名相关错误，需要更新签名算法")
                return None
                
        elif result.get('code') == 'FARM_TASK_SIGN_IN_REPEAT':
            log("今日已签到，无需重复操作。")
            return None
        else:
            error_msg = result.get('message', '未知错误')
            log(f"查询签到状态失败: {error_msg}")
            return None

    def handle_watch_video_task(self, task):
        """
        处理"看视频/广告"类任务 (优化：先complete再award)
        """
        task_name = task.get('name', '未知视频任务')
        task_id = task.get('id')
        log(f"处理任务: {task_name} (ID: {task_id})")
        
        # 根据最新抓包数据(2025.07.13)，使用已知的 taskId 和对应的签名
        # complete类型的签名
        complete_signs = {
            1783782: "0003b3c5716ed3fdbfa8eb4f42330e2dbd126067115d"
        }
        
        # award类型的签名
        award_signs = {
            1788901: "0003f475b2b91440ec1f5e22af91185bc7ae897cd6e0",
            1787408: "00035bb66b1d8deb0a71fd0c0c7abf4a77fd3d8bfc03",
            1788066: "00031c529fda41edd1535269545e3ea739b1f4f5afd7"
        }
        
        url = "https://farm-server.quark.cn/task/triggerTask"
        
        # 第一步：尝试发送 complete 请求（如果有对应的签名）
        if task_id in complete_signs:
            log("  第1步: 发送 complete 请求...")
            complete_payload = {
                "appId": "quark_baba_farm_task",
                "kps": self.kps,
                "taskId": task_id,
                "type": "complete",
                "requestId": str(uuid4()),
                "salt": "********************************",
                "sign": complete_signs[task_id]
            }
            result = self._request(url, payload=complete_payload)
            if result and result.get('success'):
                log("  ✅ 任务完成标记成功")
                # 等待一下再领奖
                time.sleep(random.uniform(1.0, 2.0))
            else:
                error_msg = result.get('message', '未知错误') if result else '无响应'
                log(f"  ❌ 任务完成标记失败: {error_msg}")
                # 即使失败也尝试继续领奖
        
        # 第二步：发送 award 请求领取奖励
        log("  第2步: 发送 award 请求领取奖励...")
        award_sign = award_signs.get(task_id, "0000000000000000000000000000000000000000")
        award_payload = {
            "appId": "quark_baba_farm_task",
            "kps": self.kps,
            "taskId": task_id,
            "type": "award",
            "requestId": str(uuid4()),
            "salt": "********************************",
            "sign": award_sign
        }
        result = self._request(url, payload=award_payload)
        
        if result and result.get('success'):
            # 尝试获取奖励信息
            prizes = result.get('data', {}).get('prizes', [])
            if prizes:
                reward_items = []
                for prize in prizes:
                    reward = prize.get('rewardItem', {})
                    amount = reward.get('amount', 0)
                    name = reward.get('name', '奖励')
                    reward_items.append(f"{name}x{amount}")
                log(f"  ✅ 任务奖励领取成功！获得: {', '.join(reward_items)}")
            else:
                log("  ✅ 任务奖励领取成功！")
            return "success"
        else:
            error_code = result.get('code', 'UNKNOWN') if result else 'NO_RESPONSE'
            error_msg = result.get('message', '未知错误') if result else '无响应'
            log(f"  ❌ 任务奖励领取失败: [{error_code}] {error_msg}")
            # 如果是签名错误，给出提示
            if 'sign' in error_msg.lower() or 'signature' in error_msg.lower():
                log("  💡 提示：签名验证失败，需要更多抓包数据分析sign生成规则")
            return "failed"

    def handle_browse_task(self, task):
        """
        处理“浏览”类任务
        """
        task_name = task.get('name', '未知浏览任务')
        task_url = task.get('url')
        log(f"处理任务: {task_name}")
        if not task_url:
            log("  🔴 任务缺少 URL，无法执行。")
            return "skipped"
        
        log(f"  🟡 此类任务需要访问特定URL: {task_url}")
        log("     通常需要在访问后调用一个“完成”或“领取奖励”的接口。")
        log("  请提供完成/领奖接口的抓包信息。")
        return "skipped"

    def handle_3rd_party_task(self, task):
        """
        处理需要跳转到第三方APP的任务
        """
        task_name = task.get('name', '未知第三方任务')
        log(f"处理任务: {task_name}")
        log(f"  🟡 此任务需要与第三方APP交互，脚本无法自动完成。跳过。")
        return "skipped"

    def process_tasks(self, tasks, summary):
        """
        任务调度器
        """
        # 任务事件到处理函数的映射
        task_handlers = {
            "quark_motivational_video": self.handle_watch_video_task,
            "baba_timelimit_event": self.handle_browse_task,
            "baba_client_event": self.handle_browse_task,
            "baba_call_welfare_event": self.handle_browse_task,
            "quark_hc_brand_ads_event": self.handle_watch_video_task,
            "quark_hc_action_ads_event": self.handle_watch_video_task,
            "baba_3rd_trigger_event": self.handle_3rd_party_task,
            # 签到是一个特例，没有在任务列表里，但我们手动处理
        }

        # 首先处理签到
        sign_result = self.sign_in()
        if sign_result:
            summary.append("✅ 每日签到: 成功")
        else:
            # 可能是已签到或者失败，但都不应该中断流程
            summary.append("✅ 每日签到: 今日已完成或无法执行")

        # 然后处理列表中的其他任务
        for task in tasks:
            if task.get('state') == 0: # 只处理未完成的任务
                event = task.get('event')
                handler = task_handlers.get(event)
                if handler:
                    try:
                        result = handler(task)
                        if result == "skipped":
                            summary.append(f"🟡 {task.get('name')}: 跳过 (需抓包)")
                        elif result == "success":
                            summary.append(f"✅ {task.get('name')}: 成功")
                        elif result == "failed":
                            summary.append(f"❌ {task.get('name')}: 失败")
                    except TaskExecutionError as e:
                        summary.append(f"❌ {task.get('name')}: 失败 - {e}")
                else:
                    # 对于没有处理器的任务，也标记为跳过
                    if event not in ["baba_farm_sub_watering", "store", "baba_farm_time_limit_day", "baba_task_modal_event"]: # 忽略那些容器或无操作任务
                        log(f"🟡 未知任务类型 '{event}'，跳过。请提供相关抓包。")
                        summary.append(f"🟡 {task.get('name')}: 跳过 (未知类型)")

    def _log_plant_progress(self, home_info):
        """
        打印植物生长进度
        """
        plant_info = home_info.get('gameInfo', {}).get('plantInfo', {}).get('seedStage', {})
        tomorrow_gift = home_info.get('activityData', {}).get('tomorrowGift', {})

        if plant_info:
            seed_name = plant_info.get('seedName', '未知植物')
            current_progress = plant_info.get('currentProgress', 0)
            total_progress = plant_info.get('totalProgress', 1)
            stage_text = plant_info.get('stageText', '')
            progress_percent = (current_progress / total_progress) * 100
            log(f"🌿 {seed_name} 进度: [{current_progress}/{total_progress}] ({progress_percent:.2f}%) - {stage_text}")
        else:
            log("🌿 无法获取植物信息。")

        if tomorrow_gift:
            tomorrow_fertilizer = tomorrow_gift.get('pointAmount', 0)
            status = tomorrow_gift.get('status', 'UNKNOWN')
            today_received = tomorrow_gift.get('todayReceived', False)
            receive_start_time = tomorrow_gift.get('receiveStartTime', 0)

            status_text = {
                'ADVANCE_NOTICE': '预告中',
                'AVAILABLE': '可领取',
                'INIT': '可领取',
                'CAN_RECEIVE': '可领取',
                'NONE': '无活动'
            }.get(status, status)

            if today_received:
                log(f"💰 tomorrow gift: {tomorrow_fertilizer}肥料 (今日已领取)")
            elif status == 'ADVANCE_NOTICE' and receive_start_time > 0:
                import time
                start_time_str = time.strftime('%m-%d %H:%M', time.localtime(receive_start_time/1000))
                log(f"💰 tomorrow gift: {tomorrow_fertilizer}肥料 ({status_text}, {start_time_str}可领)")
            else:
                log(f"💰 tomorrow gift: {tomorrow_fertilizer}肥料 ({status_text})")


    def run_all_tasks(self):
        """
        执行所有任务
        """
        summary = []
        home_info = {} # 初始化
        
        try:
            home_info = self.get_home_info()
            user_nick = home_info.get('userInfo', {}).get('nick', '未知用户')
            summary.append(f"✅ 用户: {user_nick}")
            self._log_plant_progress(home_info)

            # 1. 领取每日奖励
            self.claim_daily_reward()

            # 2. 领取每日 tomorrow gift 化肥
            self.claim_tomorrow_gift()

            # 3. 查询并处理所有非浇水任务
            task_list = self.query_task_list()
            self.process_tasks(task_list, summary)

        except TaskExecutionError as e:
            log(f"❌ 初始化或任务处理失败: {e}")
            summary.append(f"❌ 初始化或任务处理失败: {e}")
            # 即使初始化失败，也返回摘要，以便通知用户
            return "\n".join(summary)

        # 2. 循环执行浇水任务
        log("【开始检查浇水任务】")
        total_watered_times = 0
        # 重新获取一次 home_info, 因为任务可能增加了肥料
        try:
            home_info = self.get_home_info()
        except TaskExecutionError as e:
            log(f"❌ 获取浇水前状态失败: {e}")
            summary.append(f"❌ 浇水任务: 获取状态失败 - {e}")
            return "\n".join(summary)

        while True:
            account_info = home_info.get('gameInfo', {}).get('accountInfo', {})
            fertilizer_amount = account_info.get('happyPoint', 0)
            cost_per_watering = account_info.get('wateringCost', 600)
            watering_left_from_api = account_info.get('wateringLeftTimes', 0)

            if watering_left_from_api <= 0:
                log("✅ 根据API返回，今日浇水次数已用尽。")
                break

            if cost_per_watering <= 0:
                log("⚠️ 浇水成本为0，跳过浇水以防意外。")
                break

            if fertilizer_amount < cost_per_watering:
                log("✅ 肥料不足，浇水任务完成。")
                break
            
            log(f"检测到可以浇水 (剩余 {fertilizer_amount} 肥料)，开始执行第 {total_watered_times + 1} 次...")
            try:
                self.water()
                total_watered_times += 1
                log(f"第 {total_watered_times} 次浇水成功。")

                # 浇水成功后，尝试领取浇水奖励
                try:
                    self.draw_watering_reward()
                except TaskExecutionError as e:
                    log(f"检查浇水奖励时出错: {e}")

                time.sleep(random.uniform(2.5, 4.5)) # 浇水后多等一会
                home_info = self.get_home_info() # 重新获取信息
                self._log_plant_progress(home_info)

            except TaskExecutionError as e:
                log(f"❌ 浇水失败: {e}, 终止浇水。")
                summary.append(f"❌ 浇水任务: 失败 - {e}")
                break
        
        if total_watered_times > 0:
            summary.append(f"✅ 浇水任务: 总共成功执行 {total_watered_times} 次")
        else:
            summary.append("✅ 浇水任务: 无需执行或肥料不足")
        
        # 最终再获取一次信息并打印进度
        try:
            log("【任务执行后最终状态】")
            final_home_info = self.get_home_info()
            self._log_plant_progress(final_home_info)
        except TaskExecutionError as e:
            log(f"获取最终状态失败: {e}")

        return "\n".join(summary)


def main():
    log("🔔 夸克农场任务脚本开始执行...")
    
    config_str = os.environ.get('QUARK_FARM_DATA')
    if not config_str:
        log("错误：找不到环境变量 QUARK_FARM_DATA。")
        send("夸克农场任务失败", "找不到环境变量 QUARK_FARM_DATA。")
        return

    try:
        user_config = json.loads(config_str)
    except json.JSONDecodeError:
        log("错误：环境变量 QUARK_FARM_DATA 的 JSON 格式不正确。")
        send("夸克农场任务失败", "环境变量配置的 JSON 格式不正确。")
        return

    try:
        farm = QuarkFarm(user_config)
        result_summary = farm.run_all_tasks()
        log("✅ 所有任务执行完毕。")
        send("夸克农场任务报告", result_summary)
    except ValueError as e:
        log(f"配置错误: {e}")
        send("夸克农场任务失败", f"配置错误: {e}")
    except Exception as e:
        log(f"出现未知异常: {e}")
        send("夸克农场任务异常", f"执行过程中出现未知异常: {e}")


if __name__ == '__main__':
    main()
