"""
author: gaazeon
name: South-plus 签到任务
cron: 0 7 * * *
配置变量：
    export SPCOOKIE_VALUE=xxxx
"""

# 延迟执行开关：0 表示不延迟，1 表示延迟
DELAY_SWITCH = 1

import os
import sys
import time
import xml.etree.ElementTree as ET
import requests
import notify  # 导入我们创建的 notify 模块
import random

# 日志打印
def log(content, pin=None):
    msg = f'{time.strftime("%Y-%m-%d %H:%M:%S")} {content}'
    if pin is not None:
        msg = f'【{pin}】{msg}'
    print(msg)
    sys.stdout.flush()

# 获取环境变量
def get_env(key):
    return os.environ.get(key)

# 获取cookie
def get_cookie():
    return get_env('SPCOOKIE_VALUE')

# 主要任务逻辑
class TaskExecutionError(Exception):
    pass

def tasks(url, params, headers, type):
    log(f"开始执行任务: {type}")
    try:
        # 使用 Session 保持连接，添加更多反反爬虫配置
        session = requests.Session()
        session.headers.update(headers)
        
        # 设置适配器，支持重试
        from requests.adapters import HTTPAdapter
        from urllib3.util.retry import Retry
        
        retry_strategy = Retry(
            total=3,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
        )
        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        
        # 添加随机延迟避免触发防护
        import time
        time.sleep(random.uniform(0.5, 1.5))
        
        response = session.get(
            url, 
            params=params, 
            timeout=(10, 30),  # 连接超时10秒，读取超时30秒
            allow_redirects=True,
            stream=False,
            verify=True
        )
        
        # 手动设置编码
        if response.encoding == 'ISO-8859-1':
            response.encoding = 'utf-8'
        
        # 添加调试信息
        log(f"响应状态码: {response.status_code}")
        log(f"响应头 Content-Type: {response.headers.get('Content-Type', 'N/A')}")
        log(f"响应头 Content-Encoding: {response.headers.get('Content-Encoding', 'N/A')}")
        log(f"响应内容长度: {len(response.text)}")
        log(f"实际URL: {response.url}")
        
        # 检查是否被重定向到CloudFlare等验证页面
        if 'cloudflare' in response.text.lower() or 'checking your browser' in response.text.lower():
            raise TaskExecutionError("被CloudFlare拦截，请稍后重试或检查IP是否被封禁")
        
        # 检查响应状态码
        if response.status_code == 403:
            log(f"403错误 - 响应内容前200字符（原始）: {response.content[:200]}")
            log(f"403错误 - 响应内容前200字符（文本）: {response.text[:200]}")
            raise TaskExecutionError(f"访问被拒绝(403)，可能IP被封禁或触发了防爬虫机制")
        elif response.status_code != 200:
            raise TaskExecutionError(f"HTTP请求失败，状态码: {response.status_code}")
        
        # 检查响应内容是否为空
        if not response.text.strip():
            raise TaskExecutionError("服务器返回空响应")
        
        log(f"响应内容前500字符: {response.text[:500]}")
        
        # 尝试解析XML
        try:
            root = ET.fromstring(response.text)
        except ET.ParseError as parse_error:
            log(f"XML解析失败详情: {parse_error}")
            log(f"完整响应内容: {response.text}")
            raise TaskExecutionError(f"XML解析失败: {parse_error}. 响应可能不是有效的XML格式。")
        
        cdata = root.text
        if not cdata:
            raise TaskExecutionError("XML内容为空，可能是服务器返回空响应。请检查 COOKIE 设置或网络连接。")
        
        # 检查是否为未登录或Cookie失效的特定提示
        if "您还没有登录或注册" in cdata or "暂时不能使用此功能" in cdata:
            raise TaskExecutionError("检测到未登录状态，请检查并更新 SPCOOKIE_VALUE。当前Cookie可能已过期或无效。")
        elif "登录已失效" in cdata or "无效的Cookie" in cdata:
            raise TaskExecutionError("检测到无效或过期的Cookie，请更新 SPCOOKIE_VALUE。")
        
        # 尝试按tab分割解析正常响应
        values = cdata.split('\t')
        expected_length = 2 if '申请' in type else 3
        
        # 如果不是预期的格式，但也不是登录错误，则输出详细信息供调试
        if len(values) != expected_length:
            log(f"响应格式异常 - 期望长度: {expected_length}, 实际长度: {len(values)}")
            log(f"分割后的值: {values}")
            raise TaskExecutionError(f"响应格式不符合预期，请检查服务器状态。内容: {cdata}")
        
        message = values[1]
        log(f"{type}{message}")
        
        if "还没超过" in message:
            raise TaskExecutionError(message)
        
        log(f"任务 {type} 成功: {message}")
        return True

    except requests.exceptions.RequestException as e:
        raise TaskExecutionError(f"网络请求失败: {e}")
    except ET.ParseError as e:
        raise TaskExecutionError(f"解析XML失败: {e}. 可能是响应格式变化或服务器问题。")
    except TaskExecutionError as e:
        log(f"任务 {type} 失败: {str(e)}")
        # 如果是Cookie失效或未登录，建议用户更新Cookie
        if any(keyword in str(e) for keyword in ["无效或过期的Cookie", "未登录状态", "SPCOOKIE_VALUE"]):
            notify.send("South-plus 任务失败 - Cookie问题", str(e) + "\n请检查并更新您的 SPCOOKIE_VALUE 环境变量。")
        return False
    except Exception as e:
        raise TaskExecutionError(f"未知错误: {e}")

# 创建 headers 函数
def create_headers(spcookie_value, referer=None):
    headers = {
        'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'accept-language': 'zh-CN,zh;q=0.9',
        'cache-control': 'no-cache',
        'pragma': 'no-cache',
        'priority': 'u=0, i',
        'sec-ch-ua': '"Chromium";v="137", "Not/A)Brand";v="24"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"macOS"',
        'sec-fetch-dest': 'iframe',
        'sec-fetch-mode': 'navigate',
        'sec-fetch-site': 'same-origin',
        'sec-fetch-user': '?1',
        'upgrade-insecure-requests': '1',
        'cookie': spcookie_value,
        'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36'
    }
    if referer:
        headers['referer'] = referer
    return headers

# 主函数
def main():
    log('🔔 South-plus 任务脚本开始执行...')
    
    try:
        # 检查延迟执行开关
        if DELAY_SWITCH:
            delay = random.randint(1, 200)
            log(f"随机延迟执行 {delay} 秒...")
            time.sleep(delay)
        else:
            log("延迟执行已关闭，立即开始任务...")
        
        cookie = get_cookie()
        if not cookie:
            raise ValueError('请先添加环境变量 SPCOOKIE_VALUE')

        spcookie_value = cookie.replace('\n', '').replace(' ', '')
        log(f"Cookie长度: {len(spcookie_value)}")
        log(f"Cookie前100字符: {spcookie_value[:100]}...")
        
        # 检查Cookie中关键字段
        required_fields = ['eb9e6_cknum', 'eb9e6_winduser']
        missing_fields = []
        for field in required_fields:
            if field not in spcookie_value:
                missing_fields.append(field)
        
        if missing_fields:
            log(f"警告: Cookie中缺少关键字段: {missing_fields}")
        else:
            log("Cookie包含必要的认证字段")

        url = 'https://south-plus.net/plugin.php'

        # 申请任务使用 tasks.html 作为 referer
        a_headers = create_headers(spcookie_value, referer='https://south-plus.net/plugin.php?H_name-tasks.html')
        # 完成任务使用 newtasks.html.html 作为 referer
        c_headers = create_headers(spcookie_value, referer='https://south-plus.net/plugin.php?H_name-tasks-actions-newtasks.html.html')

        # 为每个请求动态生成时间戳的基础参数
        base_params = {
            'H_name': 'tasks',
            'action': 'ajax',
            'verify': '66058197',  # 更新为新的 verify 值
        }

        def get_current_params():
            nowtime = str(int(time.time() * 1000))
            log(f"生成新的时间戳: {nowtime}")
            return {**base_params, 'nowtime': nowtime}

        params_dict = {
            'ad': {'actions': 'job', 'cid': '15'},
            'aw': {'actions': 'job', 'cid': '14'},
            'cd': {'actions': 'job2', 'cid': '15'},
            'cw': {'actions': 'job2', 'cid': '14'},
        }

        results = []

        # 每个请求都使用新的时间戳
        daily_apply = tasks(url, {**get_current_params(), **params_dict['ad']}, a_headers, "申请-日常: ")
        results.append(("申请-日常", daily_apply))
        
        # 任务间延迟
        time.sleep(random.uniform(2, 4))
        
        if daily_apply:
            daily_complete = tasks(url, {**get_current_params(), **params_dict['cd']}, c_headers, "完成-日常: ")
            results.append(("完成-日常", daily_complete))
        
        # 任务间延迟
        time.sleep(random.uniform(2, 4))
        
        weekly_apply = tasks(url, {**get_current_params(), **params_dict['aw']}, a_headers, "申请-周常: ")
        results.append(("申请-周常", weekly_apply))
        
        # 任务间延迟
        time.sleep(random.uniform(2, 4))
        
        if weekly_apply:
            weekly_complete = tasks(url, {**get_current_params(), **params_dict['cw']}, c_headers, "完成-周常: ")
            results.append(("完成-周常", weekly_complete))

        log('🔔 South-plus 任务脚本执行完毕!')

        # 发送通知
        notification_message = "任务执行结果:\n"
        for task, result in results:
            notification_message += f"{task}: {'成功' if result else '失败'}\n"
        notify.send("【South-plus】 任务执行完毕", notification_message)

    except ValueError as e:
        error_msg = str(e)
        log(error_msg)
        notify.send("【South-plus】 任务失败", error_msg)
    except Exception as e:
        error_msg = f"执行过程中出现异常: {str(e)}"
        log(error_msg)
        notify.send("【South-plus】 任务异常", error_msg)

if __name__ == '__main__':
    main()
